// Example usage of EchoMind Supabase integration
// This file demonstrates how to use the memory CRUD operations

import 'package:flutter/material.dart';
import 'lib/services/supabase_service.dart';
import 'lib/models/memory_entry.dart';

class ExampleUsage {
  final SupabaseService _supabaseService = SupabaseService();

  /// Example: Save a new memory
  Future<void> saveMemoryExample() async {
    try {
      // Create a new memory entry
      final memory = MemoryEntry.create(
        userId: 'user-id-from-auth', // This will be automatically set by the service
        originalText: 'Today I learned about Flutter state management with Provider. It makes managing app state much cleaner and more organized.',
        summary: 'Flutter Learning Session',
        moodTag: 'focused',
        triggerPhrase: 'flutter learning',
        storageMode: 'cloud',
      );

      // Save to Supabase
      final savedMemory = await _supabaseService.saveMemory(memory);
      print('Memory saved with ID: ${savedMemory.id}');
      
    } catch (e) {
      print('Error saving memory: $e');
    }
  }

  /// Example: Get all user memories
  Future<void> getUserMemoriesExample() async {
    try {
      final memories = await _supabaseService.getUserMemories();
      print('Found ${memories.length} memories:');
      
      for (final memory in memories) {
        print('- ${memory.summary}: ${memory.originalText.substring(0, 50)}...');
        print('  Mood: ${memory.moodTag}, Created: ${memory.createdAt}');
      }
      
    } catch (e) {
      print('Error fetching memories: $e');
    }
  }

  /// Example: Delete a memory
  Future<void> deleteMemoryExample(String memoryId) async {
    try {
      await _supabaseService.deleteMemory(memoryId);
      print('Memory deleted successfully');
      
    } catch (e) {
      print('Error deleting memory: $e');
    }
  }

  /// Example: Authentication flow
  Future<void> authExample() async {
    try {
      // Sign up a new user
      final signUpResponse = await _supabaseService.signUp(
        '<EMAIL>',
        'securepassword123',
      );
      
      if (signUpResponse.user != null) {
        print('User signed up: ${signUpResponse.user!.email}');
      }

      // Sign in existing user
      final signInResponse = await _supabaseService.signIn(
        '<EMAIL>',
        'securepassword123',
      );
      
      if (signInResponse.user != null) {
        print('User signed in: ${signInResponse.user!.email}');
        
        // Now you can save memories for this user
        await saveMemoryExample();
        await getUserMemoriesExample();
      }

      // Sign out
      await _supabaseService.signOut();
      print('User signed out');
      
    } catch (e) {
      print('Auth error: $e');
    }
  }
}

/// Widget example showing how to use the Supabase service in a Flutter widget
class MemoryListWidget extends StatefulWidget {
  const MemoryListWidget({super.key});

  @override
  State<MemoryListWidget> createState() => _MemoryListWidgetState();
}

class _MemoryListWidgetState extends State<MemoryListWidget> {
  final SupabaseService _supabaseService = SupabaseService();
  List<MemoryEntry> _memories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadMemories();
  }

  Future<void> _loadMemories() async {
    setState(() => _isLoading = true);
    
    try {
      final memories = await _supabaseService.getUserMemories();
      setState(() {
        _memories = memories;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading memories: $e')),
        );
      }
    }
  }

  Future<void> _addSampleMemory() async {
    try {
      final memory = MemoryEntry.create(
        userId: _supabaseService.getCurrentUserId() ?? '',
        originalText: 'Sample memory created at ${DateTime.now()}',
        summary: 'Sample Memory',
        moodTag: 'happy',
        triggerPhrase: 'sample',
      );

      await _supabaseService.saveMemory(memory);
      await _loadMemories(); // Refresh the list
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Memory saved! 💙')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving memory: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Memories'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadMemories,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _memories.isEmpty
              ? const Center(
                  child: Text('No memories yet. Create your first one!'),
                )
              : ListView.builder(
                  itemCount: _memories.length,
                  itemBuilder: (context, index) {
                    final memory = _memories[index];
                    return Card(
                      margin: const EdgeInsets.all(8),
                      child: ListTile(
                        title: Text(memory.summary ?? 'Untitled'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(memory.originalText),
                            const SizedBox(height: 4),
                            Text(
                              'Mood: ${memory.moodTag ?? 'Unknown'} • ${memory.createdAt.toString().split(' ')[0]}',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                        trailing: IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () async {
                            try {
                              await _supabaseService.deleteMemory(memory.id);
                              await _loadMemories();
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(content: Text('Memory deleted')),
                                );
                              }
                            } catch (e) {
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(content: Text('Error: $e')),
                                );
                              }
                            }
                          },
                        ),
                      ),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addSampleMemory,
        child: const Icon(Icons.add),
      ),
    );
  }
}

/*
USAGE INSTRUCTIONS:

1. Authentication:
   - Users must sign up/sign in before saving memories
   - Use SupabaseService.signUp() or signIn()
   - Check authentication with getCurrentUser()

2. Saving Memories:
   - Create MemoryEntry with MemoryEntry.create()
   - Required: userId, originalText
   - Optional: summary, moodTag, triggerPhrase, storageMode

3. Retrieving Memories:
   - Use getUserMemories() to get all memories for current user
   - Memories are automatically filtered by user_id (RLS)

4. Deleting Memories:
   - Use deleteMemory(id) with the memory ID
   - Users can only delete their own memories (RLS)

5. Database Schema:
   - Table: memories
   - Fields: id, user_id, summary, original_text, created_at, updated_at, 
            mood_tag, trigger_phrase, storage_mode
   - RLS enabled for security

6. Error Handling:
   - All methods throw exceptions on error
   - Wrap in try-catch blocks
   - Check user authentication before operations
*/
