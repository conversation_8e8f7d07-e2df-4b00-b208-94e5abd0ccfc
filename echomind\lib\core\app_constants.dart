class AppConstants {
  // App Information
  static const String appName = 'EchoMind';
  static const String appVersion = '1.0.0';
  static const String appDescription =
      'Voice-based memory app for capturing and organizing thoughts';

  // API Configuration
  static const String supabaseUrl = 'https://vcgxrzrujdetjwmzapbt.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZjZ3hyenJ1amRldGp3bXphcGJ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ1NDY1MDUsImV4cCI6MjA2MDEyMjUwNX0.A6ivPMpf414Sgtj0EtNdhbumRrQqo92mHSViuO2INtE';
  static const String geminiApiKey = 'YOUR_GEMINI_API_KEY';
  static const String whisperApiKey = 'YOUR_WHISPER_API_KEY';

  // Storage Keys
  static const String userPrefsKey = 'user_preferences';
  static const String memoriesKey = 'memories';
  static const String settingsKey = 'app_settings';

  // Audio Configuration
  static const int maxRecordingDuration = 300; // 5 minutes in seconds
  static const int sampleRate = 16000;
  static const String audioFormat = 'wav';

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 2.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);

  // Memory Categories
  static const List<String> memoryCategories = [
    'Personal',
    'Work',
    'Ideas',
    'Reminders',
    'Learning',
    'Goals',
    'Thoughts',
    'Other',
  ];

  // Error Messages
  static const String networkError =
      'Network connection error. Please check your internet connection.';
  static const String audioPermissionError =
      'Microphone permission is required to record audio.';
  static const String recordingError =
      'Failed to record audio. Please try again.';
  static const String transcriptionError =
      'Failed to transcribe audio. Please try again.';
  static const String saveError = 'Failed to save memory. Please try again.';
  static const String loadError = 'Failed to load memories. Please try again.';

  // Success Messages
  static const String memorySaved = 'Memory saved successfully!';
  static const String memoryDeleted = 'Memory deleted successfully!';
  static const String memoryUpdated = 'Memory updated successfully!';
}
