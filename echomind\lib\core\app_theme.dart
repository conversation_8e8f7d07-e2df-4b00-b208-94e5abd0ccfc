import 'package:flutter/material.dart';

class AppColors {
  // <PERSON>'s Nerdy-Romantic Palette 💙🩷

  // Primary Colors
  static const Color lightBlue = Color(0xFFB3D9FF); // Main light blue
  static const Color softBlushPink = Color(0xFFFADADD); // Soft blush pink
  static const Color gentleMint = Color(0xFFD2F1E4); // Gentle mint (optional)

  // Background Gradients
  static const Color backgroundStart = Color(0xFFF5FBFF); // Pale blue
  static const Color backgroundEnd = Color(0xFFFFF5F7); // Pale pink
  static const Color pureWhite = Color(0xFFFFFFFF);

  // Text Colors
  static const Color textPrimary = Color(0xFF1F2A37); // Deep navy/charcoal
  static const Color textSecondary = Color(0xFF6B7280); // Softer gray
  static const Color textLight = Color(0xFF9CA3AF); // Light gray

  // Accent Colors for Moods
  static const Color calmBlue = Color(0xFF93C5FD); // Calm memories
  static const Color emotionalPink = Color(0xFFF8BBD9); // Emotional memories
  static const Color focusedMint = Color(0xFFA7F3D0); // Focused memories

  // Interactive Colors
  static const Color voiceButtonBlue = Color(0xFF60A5FA); // Voice button
  static const Color pinkGlow = Color(0xFFEC4899); // Pink glow/ripple
  static const Color successGreen = Color(0xFF34D399); // Success states
  static const Color warningAmber = Color(0xFFFBBF24); // Warning states
  static const Color errorRose = Color(0xFFF87171); // Error states

  // Dark Mode Colors (softer, romantic dark theme)
  static const Color darkBackground = Color(0xFF1E1B2E); // Deep purple-navy
  static const Color darkSurface = Color(0xFF2D2A3D); // Slightly lighter
  static const Color darkTextPrimary = Color(0xFFE5E7EB); // Light text
  static const Color darkTextSecondary = Color(0xFFD1D5DB); // Secondary text
  static const Color darkAccent = Color(0xFF8B5CF6); // Purple accent for dark

  // Semantic Colors
  static const Color loveNotes = Color(0xFFFF69B4); // Hot pink for love
  static const Color studyMode = Color(0xFF4F46E5); // Indigo for study
  static const Color mintTea = Color(0xFF10B981); // Emerald for calm
}

class AppTheme {
  // Gradient Definitions
  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      AppColors.backgroundStart,
      AppColors.pureWhite,
      AppColors.backgroundEnd,
    ],
    stops: [0.0, 0.5, 1.0],
  );

  static const LinearGradient navGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [AppColors.lightBlue, AppColors.softBlushPink],
  );

  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [AppColors.pureWhite, Color(0xFFFEFEFE)],
  );

  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.lightBlue,
        brightness: Brightness.light,
        primary: AppColors.lightBlue,
        secondary: AppColors.softBlushPink,
        surface: AppColors.pureWhite,
        error: AppColors.errorRose,
        onPrimary: AppColors.textPrimary,
        onSecondary: AppColors.textPrimary,
        onSurface: AppColors.textPrimary,
      ),
      scaffoldBackgroundColor: Colors.transparent, // We'll use gradient
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          color: AppColors.textPrimary,
          fontSize: 22,
          fontWeight: FontWeight.w700,
          letterSpacing: 0.5,
        ),
        iconTheme: const IconThemeData(color: AppColors.textPrimary, size: 24),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.lightBlue,
          foregroundColor: AppColors.textPrimary,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 2,
          shadowColor: AppColors.lightBlue.withValues(alpha: 0.3),
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColors.voiceButtonBlue,
        foregroundColor: AppColors.pureWhite,
        elevation: 6,
        splashColor: AppColors.pinkGlow,
      ),
      cardTheme: CardThemeData(
        color: AppColors.pureWhite,
        elevation: 3,
        shadowColor: AppColors.lightBlue.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.pureWhite.withValues(alpha: 0.8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(
            color: AppColors.lightBlue.withValues(alpha: 0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(
            color: AppColors.softBlushPink,
            width: 2,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(
            color: AppColors.lightBlue.withValues(alpha: 0.2),
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 16,
        ),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.darkAccent,
        brightness: Brightness.dark,
        primary: AppColors.darkAccent,
        secondary: AppColors.softBlushPink,
        surface: AppColors.darkSurface,
        error: AppColors.errorRose,
        onPrimary: AppColors.darkTextPrimary,
        onSecondary: AppColors.darkTextPrimary,
        onSurface: AppColors.darkTextPrimary,
      ),
      scaffoldBackgroundColor: AppColors.darkBackground,
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.darkTextPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          color: AppColors.darkTextPrimary,
          fontSize: 22,
          fontWeight: FontWeight.w700,
          letterSpacing: 0.5,
        ),
        iconTheme: const IconThemeData(
          color: AppColors.darkTextPrimary,
          size: 24,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.darkAccent,
          foregroundColor: AppColors.darkTextPrimary,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 2,
          shadowColor: AppColors.darkAccent.withValues(alpha: 0.3),
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColors.darkAccent,
        foregroundColor: AppColors.darkTextPrimary,
        elevation: 6,
        splashColor: AppColors.pinkGlow,
      ),
      cardTheme: CardThemeData(
        color: AppColors.darkSurface,
        elevation: 3,
        shadowColor: AppColors.darkAccent.withValues(alpha: 0.2),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.darkSurface.withValues(alpha: 0.8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(
            color: AppColors.darkAccent.withValues(alpha: 0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(
            color: AppColors.softBlushPink,
            width: 2,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(
            color: AppColors.darkAccent.withValues(alpha: 0.2),
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 16,
        ),
      ),
    );
  }
}
