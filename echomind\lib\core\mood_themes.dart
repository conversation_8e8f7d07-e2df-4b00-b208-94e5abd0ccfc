import 'package:flutter/material.dart';

/// Mood themes for emotional polish in EchoMind
class MoodThemes {
  // Mood emoji mapping
  static const Map<String, String> moodEmojis = {
    'happy': '😊',
    'stressed': '😰',
    'reflective': '🤔',
    'neutral': '😐',
    'sad': '😢',
    'motivated': '💪',
    'peaceful': '😌',
    'excited': '🎉',
    'grateful': '🙏',
    'thoughtful': '💭',
    'concerned': '😟',
    'urgent': '⚡',
    'reminder': '📝',
    'idea': '💡',
    'goal': '🎯',
  };

  // Mood color themes
  static const Map<String, Color> moodColors = {
    'happy': Color(0xFFFFE082), // Warm yellow
    'stressed': Color(0xFFFFAB91), // Soft orange-red
    'reflective': Color(0xFFB39DDB), // Soft purple
    'neutral': Color(0xFFE0E0E0), // Light gray
    'sad': Color(0xFF90CAF9), // Soft blue
    'motivated': Color(0xFF81C784), // Energetic green
    'peaceful': Color(0xFFA5D6A7), // Calm green
    'excited': Color(0xFFFFCC02), // Bright yellow
    'grateful': Color(0xFFFFAB40), // Warm orange
    'thoughtful': Color(0xFFCE93D8), // Light purple
    'concerned': Color(0xFFFFCC80), // Muted orange
    'urgent': Color(0xFFEF5350), // Alert red
    'reminder': Color(0xFF64B5F6), // Task blue
    'idea': Color(0xFFFFD54F), // Bright yellow
    'goal': Color(0xFF4CAF50), // Success green
  };

  // Mood gradient colors for backgrounds
  static const Map<String, List<Color>> moodGradients = {
    'happy': [Color(0xFFFFE082), Color(0xFFFFF176)],
    'stressed': [Color(0xFFFFAB91), Color(0xFFFFCC80)],
    'reflective': [Color(0xFFB39DDB), Color(0xFFCE93D8)],
    'neutral': [Color(0xFFE0E0E0), Color(0xFFEEEEEE)],
    'sad': [Color(0xFF90CAF9), Color(0xFFBBDEFB)],
    'motivated': [Color(0xFF81C784), Color(0xFFA5D6A7)],
    'peaceful': [Color(0xFFA5D6A7), Color(0xFFC8E6C9)],
    'excited': [Color(0xFFFFCC02), Color(0xFFFFD54F)],
    'grateful': [Color(0xFFFFAB40), Color(0xFFFFCC80)],
    'thoughtful': [Color(0xFFCE93D8), Color(0xFFE1BEE7)],
    'concerned': [Color(0xFFFFCC80), Color(0xFFFFE0B2)],
    'urgent': [Color(0xFFEF5350), Color(0xFFE57373)],
    'reminder': [Color(0xFF64B5F6), Color(0xFF90CAF9)],
    'idea': [Color(0xFFFFD54F), Color(0xFFFFE082)],
    'goal': [Color(0xFF4CAF50), Color(0xFF66BB6A)],
  };

  // Mood descriptions for UI
  static const Map<String, String> moodDescriptions = {
    'happy': 'Joyful & Positive',
    'stressed': 'Anxious & Overwhelmed',
    'reflective': 'Thoughtful & Contemplative',
    'neutral': 'Calm & Factual',
    'sad': 'Melancholy & Upset',
    'motivated': 'Determined & Ambitious',
    'peaceful': 'Serene & Content',
    'excited': 'Energetic & Enthusiastic',
    'grateful': 'Thankful & Appreciative',
    'thoughtful': 'Pensive & Considerate',
    'concerned': 'Worried & Cautious',
    'urgent': 'Time-sensitive & Critical',
    'reminder': 'Task-oriented & Practical',
    'idea': 'Creative & Innovative',
    'goal': 'Achievement-focused',
  };

  /// Get emoji for mood
  static String getEmoji(String? mood) {
    return moodEmojis[mood] ?? moodEmojis['neutral']!;
  }

  /// Get color for mood
  static Color getColor(String? mood) {
    return moodColors[mood] ?? moodColors['neutral']!;
  }

  /// Get gradient colors for mood
  static List<Color> getGradient(String? mood) {
    return moodGradients[mood] ?? moodGradients['neutral']!;
  }

  /// Get description for mood
  static String getDescription(String? mood) {
    return moodDescriptions[mood] ?? moodDescriptions['neutral']!;
  }

  /// Get all available moods for selection
  static List<String> getAllMoods() {
    return moodEmojis.keys.toList();
  }

  /// Get mood options for UI selection
  static Map<String, String> getMoodOptions() {
    return {
      'happy': '😊 Happy',
      'excited': '🎉 Excited',
      'grateful': '🙏 Grateful',
      'motivated': '💪 Motivated',
      'peaceful': '😌 Peaceful',
      'reflective': '🤔 Reflective',
      'thoughtful': '💭 Thoughtful',
      'neutral': '😐 Neutral',
      'concerned': '😟 Concerned',
      'stressed': '😰 Stressed',
      'sad': '😢 Sad',
      'urgent': '⚡ Urgent',
      'reminder': '📝 Reminder',
      'idea': '💡 Idea',
      'goal': '🎯 Goal',
    };
  }

  /// Create a mood-themed container decoration
  static BoxDecoration createMoodDecoration(String? mood, {double opacity = 0.1}) {
    final colors = getGradient(mood);
    return BoxDecoration(
      gradient: LinearGradient(
        colors: colors.map((color) => color.withValues(alpha: opacity)).toList(),
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: getColor(mood).withValues(alpha: 0.3),
        width: 1,
      ),
    );
  }

  /// Create a mood-themed card
  static Widget createMoodCard({
    required Widget child,
    String? mood,
    double opacity = 0.1,
    EdgeInsets? padding,
  }) {
    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      decoration: createMoodDecoration(mood, opacity: opacity),
      child: child,
    );
  }

  /// Get text color that contrasts well with mood color
  static Color getTextColor(String? mood) {
    final color = getColor(mood);
    // Use luminance to determine if we need dark or light text
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  /// Create a mood indicator chip
  static Widget createMoodChip(String? mood, {bool showText = true}) {
    final emoji = getEmoji(mood);
    final color = getColor(mood);
    final description = getDescription(mood);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.5),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            emoji,
            style: const TextStyle(fontSize: 16),
          ),
          if (showText) ...[
            const SizedBox(width: 6),
            Text(
              description,
              style: TextStyle(
                color: getTextColor(mood),
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Get mood intensity (for future features like mood tracking)
  static double getMoodIntensity(String? mood) {
    const intensityMap = {
      'excited': 0.9,
      'happy': 0.8,
      'motivated': 0.8,
      'grateful': 0.7,
      'peaceful': 0.6,
      'thoughtful': 0.5,
      'reflective': 0.5,
      'neutral': 0.5,
      'concerned': 0.4,
      'sad': 0.3,
      'stressed': 0.2,
      'urgent': 0.1,
    };
    
    return intensityMap[mood] ?? 0.5;
  }

  /// Get complementary mood suggestions
  static List<String> getComplementaryMoods(String? mood) {
    const complementaryMap = {
      'stressed': ['peaceful', 'calm', 'motivated'],
      'sad': ['grateful', 'hopeful', 'peaceful'],
      'neutral': ['happy', 'motivated', 'reflective'],
      'happy': ['grateful', 'excited', 'peaceful'],
      'motivated': ['goal', 'focused', 'determined'],
    };
    
    return complementaryMap[mood] ?? ['neutral', 'peaceful', 'reflective'];
  }
}
