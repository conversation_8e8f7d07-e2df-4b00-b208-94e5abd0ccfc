import '../services/gemini_service.dart';

/// Example demonstrating AI integration for voice memory summarization
class AIIntegrationExample {
  static final GeminiService _geminiService = GeminiService();

  /// Test the AI memory summarization with various voice inputs
  static Future<void> testMemorySummarization() async {
    // Test cases with expected behavior
    final testCases = [
      {
        'input': '<PERSON><PERSON>, remind me to check my email for the class schedule and message <PERSON><PERSON>.',
        'expected_pattern': 'Check email for class schedule; message <PERSON><PERSON>',
      },
      {
        'input': 'Hey <PERSON><PERSON>, I need to buy groceries tomorrow - milk, bread, and eggs from the store.',
        'expected_pattern': 'Buy groceries: milk, bread, eggs',
      },
      {
        'input': 'Note to self: call mom about dinner plans for Sunday and ask about her doctor appointment.',
        'expected_pattern': 'Call mom about Sunday dinner and doctor appointment',
      },
      {
        'input': '<PERSON><PERSON>, f<PERSON><PERSON> to submit the assignment before 5 PM and review the presentation slides.',
        'expected_pattern': 'Submit assignment before 5 PM; review slides',
      },
      {
        'input': 'Remember to water the plants, feed the cat, and lock the door before leaving.',
        'expected_pattern': 'Water plants, feed cat, lock door before leaving',
      },
      {
        'input': 'Meeting with <PERSON> at 3 PM to discuss the project timeline and budget requirements.',
        'expected_pattern': 'Meeting with <PERSON> at 3 PM: project timeline and budget',
      },
    ];

    // Test each case
    for (int i = 0; i < testCases.length; i++) {
      final testCase = testCases[i];
      final input = testCase['input'] as String;
      final expectedPattern = testCase['expected_pattern'] as String;

      try {
        // Test the AI summarization
        final summary = await _geminiService.summarizeMemory(input);
        
        // Display results
        _displayTestResult(i + 1, input, summary, expectedPattern);
        
        // Small delay between requests to avoid rate limiting
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        // AI integration test failed for case ${i + 1}: $e
        
        // Test fallback mechanism
        final fallbackSummary = _testFallbackSummarization(input);
        _displayFallbackResult(i + 1, input, fallbackSummary);
      }
    }
  }

  /// Test fallback summarization when AI fails
  static String _testFallbackSummarization(String input) {
    // Simulate the fallback logic from GeminiService
    String cleanContent = input
        .toLowerCase()
        .replaceAll(
          RegExp(
            r'(younes|hey younes|fkerni|note to self|remind me to|remind me)',
            caseSensitive: false,
          ),
          '',
        )
        .trim();
    
    final words = cleanContent
        .split(' ')
        .where((word) => word.isNotEmpty && word.length > 2)
        .take(8)
        .join(' ');
    
    if (words.isEmpty) {
      return 'Voice memory captured';
    }
    
    return words.length > 100 ? '${words.substring(0, 97)}...' : words;
  }

  /// Display test result
  static void _displayTestResult(int testNumber, String input, String summary, String expectedPattern) {
    // AI Integration Test $testNumber
    // Input: "$input"
    // AI Summary: "$summary"
    // Expected Pattern: "$expectedPattern"
    // Length: ${summary.length} characters
    // Status: ${summary.length <= 100 ? 'PASS' : 'FAIL'} (length check)
    // ---
  }

  /// Display fallback result
  static void _displayFallbackResult(int testNumber, String input, String fallbackSummary) {
    // Fallback Test $testNumber
    // Input: "$input"
    // Fallback Summary: "$fallbackSummary"
    // Length: ${fallbackSummary.length} characters
    // Status: FALLBACK WORKING
    // ---
  }

  /// Test error handling scenarios
  static Future<void> testErrorHandling() async {
    final errorTestCases = [
      '', // Empty input
      '   ', // Whitespace only
      'a', // Very short input
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ' * 10, // Very long input
      '!@#\$%^&*()_+', // Special characters only
      '123456789', // Numbers only
    ];

    for (int i = 0; i < errorTestCases.length; i++) {
      final input = errorTestCases[i];
      
      try {
        final summary = await _geminiService.summarizeMemory(input);
        // Error handling test ${i + 1}: Input="$input" -> Summary="$summary"
      } catch (e) {
        // Error handling test ${i + 1}: Input="$input" -> Error: $e
      }
    }
  }

  /// Test integration with voice recording workflow
  static Future<void> testVoiceWorkflowIntegration() async {
    // Simulate the complete voice-to-memory workflow
    final voiceInputs = [
      'Hey Younes, remind me to call the dentist tomorrow morning',
      'Note to self: pick up dry cleaning and buy birthday gift for mom',
      'Younes, fkerni to review the contract and send feedback by Friday',
    ];

    for (final voiceInput in voiceInputs) {
      try {
        // Step 1: Voice detection (simulated)
        // Voice detected: "$voiceInput"
        
        // Step 2: Transcription (simulated - already have text)
        final transcription = voiceInput;
        
        // Step 3: AI summarization
        final summary = await _geminiService.summarizeMemory(transcription);
        
        // Step 4: Generate additional metadata
        final title = await _geminiService.generateTitle(transcription);
        final tags = await _geminiService.generateTags(transcription);
        final category = await _geminiService.suggestCategory(transcription);
        
        // Display complete workflow result
        _displayWorkflowResult(voiceInput, summary, title, tags, category);
        
        await Future.delayed(const Duration(milliseconds: 1000));
      } catch (e) {
        // Workflow integration test failed: $e
      }
    }
  }

  /// Display workflow result
  static void _displayWorkflowResult(
    String input,
    String summary,
    String title,
    List<String> tags,
    String category,
  ) {
    // Complete Voice Workflow Test
    // Original: "$input"
    // Summary: "$summary"
    // Title: "$title"
    // Tags: ${tags.join(', ')}
    // Category: "$category"
    // Memory Quality: ${_assessMemoryQuality(summary, title, tags)}
    // ===
  }

  /// Assess the quality of generated memory
  static String _assessMemoryQuality(String summary, String title, List<String> tags) {
    int score = 0;
    
    // Check summary quality
    if (summary.isNotEmpty && summary.length <= 100) score += 2;
    if (summary.contains(RegExp(r'\b(check|call|buy|review|submit|meet)\b', caseSensitive: false))) score += 1;
    
    // Check title quality
    if (title.isNotEmpty && title.length <= 50) score += 1;
    
    // Check tags quality
    if (tags.isNotEmpty && tags.length <= 5) score += 1;
    
    if (score >= 4) return 'EXCELLENT';
    if (score >= 3) return 'GOOD';
    if (score >= 2) return 'FAIR';
    return 'NEEDS_IMPROVEMENT';
  }

  /// Run all AI integration tests
  static Future<void> runAllTests() async {
    // Starting AI Integration Tests...
    
    // Test 1: Memory Summarization
    await testMemorySummarization();
    
    // Test 2: Error Handling
    await testErrorHandling();
    
    // Test 3: Voice Workflow Integration
    await testVoiceWorkflowIntegration();
    
    // AI Integration Tests Completed!
  }
}
