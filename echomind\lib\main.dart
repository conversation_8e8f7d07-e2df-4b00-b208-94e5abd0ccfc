import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/home_screen.dart';
import 'screens/voice_capture_screen.dart';
import 'screens/memory_timeline_screen.dart';
import 'core/app_theme.dart';
import 'state/app_state_provider.dart';
import 'state/recording_provider.dart';

void main() {
  runApp(const EchoMindApp());
}

class EchoMindApp extends StatelessWidget {
  const EchoMindApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AppStateProvider()),
        ChangeNotifierProvider(create: (_) => RecordingProvider()),
      ],
      child: MaterialApp(
        title: 'EchoMind',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        initialRoute: '/',
        routes: {
          '/': (context) => const HomeScreen(),
          '/voice-capture': (context) => const VoiceCaptureScreen(),
          '/memory-timeline': (context) => const MemoryTimelineScreen(),
        },
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
