import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'screens/home_screen.dart';
import 'screens/voice_capture_screen.dart';
import 'screens/voice_recording_screen.dart';
import 'screens/always_on_settings_screen.dart';
import 'screens/memory_timeline_screen.dart';
import 'screens/memory_query_screen.dart';
import 'screens/auth_screen.dart';
import 'core/app_theme.dart';
import 'core/app_constants.dart';
import 'state/app_state_provider.dart';
import 'state/recording_provider.dart';
import 'state/voice_recording_provider.dart';
import 'state/always_on_voice_provider.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Supabase.initialize(
    url: AppConstants.supabaseUrl,
    anonKey: AppConstants.supabaseAnonKey,
  );

  runApp(const EchoMindApp());
}

class EchoMindApp extends StatelessWidget {
  const EchoMindApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AppStateProvider()),
        ChangeNotifierProvider(create: (_) => RecordingProvider()),
        ChangeNotifierProvider(create: (_) => VoiceRecordingProvider()),
        ChangeNotifierProvider(create: (_) => AlwaysOnVoiceProvider()),
      ],
      child: MaterialApp(
        title: 'EchoMind',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        initialRoute: '/',
        routes: {
          '/': (context) => const HomeScreen(),
          '/auth': (context) => const AuthScreen(),
          '/voice-capture': (context) => const VoiceCaptureScreen(),
          '/voice-recording': (context) => const VoiceRecordingScreen(),
          '/always-on-settings': (context) => const AlwaysOnSettingsScreen(),
          '/memory-timeline': (context) => const MemoryTimelineScreen(),
          '/memory-query': (context) => const MemoryQueryScreen(),
        },
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
