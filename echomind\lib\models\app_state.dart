import 'package:equatable/equatable.dart';
import 'memory_entry.dart';
import 'user.dart';

enum AppStatus { initial, loading, loaded, error }

enum RecordingStatus { idle, recording, processing, completed, error }

class AppState extends Equatable {
  final AppStatus status;
  final User? user;
  final List<MemoryEntry> memories;
  final List<MemoryEntry> filteredMemories;
  final String searchQuery;
  final String selectedCategory;
  final bool isRecording;
  final RecordingStatus recordingStatus;
  final String? errorMessage;
  final bool isOnline;

  const AppState({
    this.status = AppStatus.initial,
    this.user,
    this.memories = const [],
    this.filteredMemories = const [],
    this.searchQuery = '',
    this.selectedCategory = 'All',
    this.isRecording = false,
    this.recordingStatus = RecordingStatus.idle,
    this.errorMessage,
    this.isOnline = true,
  });

  AppState copyWith({
    AppStatus? status,
    User? user,
    List<MemoryEntry>? memories,
    List<MemoryEntry>? filteredMemories,
    String? searchQuery,
    String? selectedCategory,
    bool? isRecording,
    RecordingStatus? recordingStatus,
    String? errorMessage,
    bool? isOnline,
  }) {
    return AppState(
      status: status ?? this.status,
      user: user ?? this.user,
      memories: memories ?? this.memories,
      filteredMemories: filteredMemories ?? this.filteredMemories,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      isRecording: isRecording ?? this.isRecording,
      recordingStatus: recordingStatus ?? this.recordingStatus,
      errorMessage: errorMessage ?? this.errorMessage,
      isOnline: isOnline ?? this.isOnline,
    );
  }

  // Helper methods
  bool get isLoading => status == AppStatus.loading;
  bool get hasError => status == AppStatus.error;
  bool get isAuthenticated => user != null;
  bool get hasMemories => memories.isNotEmpty;
  bool get isSearching => searchQuery.isNotEmpty;
  bool get isFiltering => selectedCategory != 'All';

  List<String> get availableCategories {
    final categories = memories.map((m) => m.category).toSet().toList();
    categories.sort();
    return ['All', ...categories];
  }

  List<MemoryEntry> get favoriteMemories {
    return memories.where((m) => m.isFavorite).toList();
  }

  List<MemoryEntry> get recentMemories {
    final sorted = List<MemoryEntry>.from(memories);
    sorted.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return sorted.take(10).toList();
  }

  Map<String, int> get memoriesByCategory {
    final Map<String, int> categoryCount = {};
    for (final memory in memories) {
      categoryCount[memory.category] = (categoryCount[memory.category] ?? 0) + 1;
    }
    return categoryCount;
  }

  @override
  List<Object?> get props => [
        status,
        user,
        memories,
        filteredMemories,
        searchQuery,
        selectedCategory,
        isRecording,
        recordingStatus,
        errorMessage,
        isOnline,
      ];

  @override
  String toString() {
    return 'AppState(status: $status, memoriesCount: ${memories.length}, isRecording: $isRecording)';
  }
}
