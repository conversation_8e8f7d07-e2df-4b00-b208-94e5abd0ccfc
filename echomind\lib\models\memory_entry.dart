import 'package:equatable/equatable.dart';

class MemoryEntry extends Equatable {
  final String id;
  final String title;
  final String content;
  final String? transcription;
  final String? audioFilePath;
  final String category;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isFavorite;
  final double? audioDuration; // in seconds
  final Map<String, dynamic>? metadata;

  const MemoryEntry({
    required this.id,
    required this.title,
    required this.content,
    this.transcription,
    this.audioFilePath,
    required this.category,
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
    this.isFavorite = false,
    this.audioDuration,
    this.metadata,
  });

  // Factory constructor for creating from JSON
  factory MemoryEntry.fromJson(Map<String, dynamic> json) {
    return MemoryEntry(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      transcription: json['transcription'] as String?,
      audioFilePath: json['audioFilePath'] as String?,
      category: json['category'] as String,
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isFavorite: json['isFavorite'] as bool? ?? false,
      audioDuration: (json['audioDuration'] as num?)?.toDouble(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'transcription': transcription,
      'audioFilePath': audioFilePath,
      'category': category,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isFavorite': isFavorite,
      'audioDuration': audioDuration,
      'metadata': metadata,
    };
  }

  // Factory constructor for creating a new memory
  factory MemoryEntry.create({
    required String title,
    required String content,
    String? transcription,
    String? audioFilePath,
    required String category,
    List<String> tags = const [],
    bool isFavorite = false,
    double? audioDuration,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    return MemoryEntry(
      id: _generateId(),
      title: title,
      content: content,
      transcription: transcription,
      audioFilePath: audioFilePath,
      category: category,
      tags: tags,
      createdAt: now,
      updatedAt: now,
      isFavorite: isFavorite,
      audioDuration: audioDuration,
      metadata: metadata,
    );
  }

  // Generate a unique ID
  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Copy with method for updating properties
  MemoryEntry copyWith({
    String? id,
    String? title,
    String? content,
    String? transcription,
    String? audioFilePath,
    String? category,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isFavorite,
    double? audioDuration,
    Map<String, dynamic>? metadata,
  }) {
    return MemoryEntry(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      transcription: transcription ?? this.transcription,
      audioFilePath: audioFilePath ?? this.audioFilePath,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      isFavorite: isFavorite ?? this.isFavorite,
      audioDuration: audioDuration ?? this.audioDuration,
      metadata: metadata ?? this.metadata,
    );
  }

  // Helper methods
  bool get hasAudio => audioFilePath != null && audioFilePath!.isNotEmpty;
  bool get hasTranscription => transcription != null && transcription!.isNotEmpty;
  bool get hasTags => tags.isNotEmpty;
  
  String get formattedDuration {
    if (audioDuration == null) return '';
    final duration = Duration(seconds: audioDuration!.round());
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  List<Object?> get props => [
        id,
        title,
        content,
        transcription,
        audioFilePath,
        category,
        tags,
        createdAt,
        updatedAt,
        isFavorite,
        audioDuration,
        metadata,
      ];

  @override
  String toString() {
    return 'MemoryEntry(id: $id, title: $title, category: $category, createdAt: $createdAt)';
  }
}
