import 'package:equatable/equatable.dart';

class MemoryEntry extends Equatable {
  final String id;
  final String userId;
  final String? summary; // Gemini-generated summary
  final String originalText; // Full transcribed voice input
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? moodTag; // happy, sad, curious, etc.
  final String? triggerPhrase; // Phrase used to start memory capture
  final String storageMode; // "local" or "cloud"

  // Additional fields for app functionality (not in Supabase)
  final String? audioFilePath;
  final bool isFavorite;
  final double? audioDuration; // in seconds
  final List<String> tags;
  final Map<String, dynamic>? metadata;

  const MemoryEntry({
    required this.id,
    required this.userId,
    this.summary,
    required this.originalText,
    required this.createdAt,
    required this.updatedAt,
    this.moodTag,
    this.triggerPhrase,
    this.storageMode = 'cloud',
    this.audioFilePath,
    this.isFavorite = false,
    this.audioDuration,
    this.tags = const [],
    this.metadata,
  });

  // Factory constructor for creating from JSON (Supabase format)
  factory MemoryEntry.fromJson(Map<String, dynamic> json) {
    return MemoryEntry(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      summary: json['summary'] as String?,
      originalText: json['original_text'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      moodTag: json['mood_tag'] as String?,
      triggerPhrase: json['trigger_phrase'] as String?,
      storageMode: json['storage_mode'] as String? ?? 'cloud',
      // Local app fields (not stored in Supabase)
      audioFilePath: json['audioFilePath'] as String?,
      isFavorite: json['isFavorite'] as bool? ?? false,
      audioDuration: (json['audioDuration'] as num?)?.toDouble(),
      tags: (json['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  // Convert to JSON (Supabase format)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'summary': summary,
      'original_text': originalText,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'mood_tag': moodTag,
      'trigger_phrase': triggerPhrase,
      'storage_mode': storageMode,
      // Local app fields (not sent to Supabase)
      'audioFilePath': audioFilePath,
      'isFavorite': isFavorite,
      'audioDuration': audioDuration,
      'tags': tags,
      'metadata': metadata,
    };
  }

  // Factory constructor for creating a new memory
  factory MemoryEntry.create({
    required String userId,
    required String originalText,
    String? summary,
    String? moodTag,
    String? triggerPhrase,
    String storageMode = 'cloud',
    String? audioFilePath,
    List<String> tags = const [],
    bool isFavorite = false,
    double? audioDuration,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    return MemoryEntry(
      id: _generateId(),
      userId: userId,
      summary: summary,
      originalText: originalText,
      createdAt: now,
      updatedAt: now,
      moodTag: moodTag,
      triggerPhrase: triggerPhrase,
      storageMode: storageMode,
      audioFilePath: audioFilePath,
      tags: tags,
      isFavorite: isFavorite,
      audioDuration: audioDuration,
      metadata: metadata,
    );
  }

  // Generate a unique ID
  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  // Copy with method for updating properties
  MemoryEntry copyWith({
    String? id,
    String? userId,
    String? summary,
    String? originalText,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? moodTag,
    String? triggerPhrase,
    String? storageMode,
    String? audioFilePath,
    bool? isFavorite,
    double? audioDuration,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) {
    return MemoryEntry(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      summary: summary ?? this.summary,
      originalText: originalText ?? this.originalText,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      moodTag: moodTag ?? this.moodTag,
      triggerPhrase: triggerPhrase ?? this.triggerPhrase,
      storageMode: storageMode ?? this.storageMode,
      audioFilePath: audioFilePath ?? this.audioFilePath,
      isFavorite: isFavorite ?? this.isFavorite,
      audioDuration: audioDuration ?? this.audioDuration,
      tags: tags ?? this.tags,
      metadata: metadata ?? this.metadata,
    );
  }

  // Backward compatibility getters
  String get title => summary ?? originalText.split(' ').take(5).join(' ');
  String get content => originalText;
  String? get transcription => originalText;
  String get category => moodTag ?? 'Personal';

  // Helper methods
  bool get hasAudio => audioFilePath != null && audioFilePath!.isNotEmpty;
  bool get hasTranscription => originalText.isNotEmpty;
  bool get hasTags => tags.isNotEmpty;

  String get formattedDuration {
    if (audioDuration == null) return '';
    final duration = Duration(seconds: audioDuration!.round());
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  List<Object?> get props => [
    id,
    userId,
    summary,
    originalText,
    createdAt,
    updatedAt,
    moodTag,
    triggerPhrase,
    storageMode,
    audioFilePath,
    isFavorite,
    audioDuration,
    tags,
    metadata,
  ];

  @override
  String toString() {
    final truncatedText = originalText.length > 50
        ? '${originalText.substring(0, 50)}...'
        : originalText;
    return 'MemoryEntry(id: $id, userId: $userId, originalText: $truncatedText, createdAt: $createdAt)';
  }
}
