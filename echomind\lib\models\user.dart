import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String email;
  final String? displayName;
  final String? avatarUrl;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final UserPreferences preferences;

  const User({
    required this.id,
    required this.email,
    this.displayName,
    this.avatarUrl,
    required this.createdAt,
    required this.lastLoginAt,
    required this.preferences,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      email: json['email'] as String,
      displayName: json['displayName'] as String?,
      avatarUrl: json['avatarUrl'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastLoginAt: DateTime.parse(json['lastLoginAt'] as String),
      preferences: UserPreferences.fromJson(
        json['preferences'] as Map<String, dynamic>? ?? {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'avatarUrl': avatarUrl,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt.toIso8601String(),
      'preferences': preferences.toJson(),
    };
  }

  User copyWith({
    String? id,
    String? email,
    String? displayName,
    String? avatarUrl,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    UserPreferences? preferences,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        displayName,
        avatarUrl,
        createdAt,
        lastLoginAt,
        preferences,
      ];
}

class UserPreferences extends Equatable {
  final bool darkMode;
  final String language;
  final bool notificationsEnabled;
  final bool autoTranscribe;
  final String defaultCategory;
  final int maxRecordingDuration;
  final bool autoSave;
  final bool cloudSync;

  const UserPreferences({
    this.darkMode = false,
    this.language = 'en',
    this.notificationsEnabled = true,
    this.autoTranscribe = true,
    this.defaultCategory = 'Personal',
    this.maxRecordingDuration = 300, // 5 minutes
    this.autoSave = true,
    this.cloudSync = true,
  });

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      darkMode: json['darkMode'] as bool? ?? false,
      language: json['language'] as String? ?? 'en',
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
      autoTranscribe: json['autoTranscribe'] as bool? ?? true,
      defaultCategory: json['defaultCategory'] as String? ?? 'Personal',
      maxRecordingDuration: json['maxRecordingDuration'] as int? ?? 300,
      autoSave: json['autoSave'] as bool? ?? true,
      cloudSync: json['cloudSync'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'darkMode': darkMode,
      'language': language,
      'notificationsEnabled': notificationsEnabled,
      'autoTranscribe': autoTranscribe,
      'defaultCategory': defaultCategory,
      'maxRecordingDuration': maxRecordingDuration,
      'autoSave': autoSave,
      'cloudSync': cloudSync,
    };
  }

  UserPreferences copyWith({
    bool? darkMode,
    String? language,
    bool? notificationsEnabled,
    bool? autoTranscribe,
    String? defaultCategory,
    int? maxRecordingDuration,
    bool? autoSave,
    bool? cloudSync,
  }) {
    return UserPreferences(
      darkMode: darkMode ?? this.darkMode,
      language: language ?? this.language,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      autoTranscribe: autoTranscribe ?? this.autoTranscribe,
      defaultCategory: defaultCategory ?? this.defaultCategory,
      maxRecordingDuration: maxRecordingDuration ?? this.maxRecordingDuration,
      autoSave: autoSave ?? this.autoSave,
      cloudSync: cloudSync ?? this.cloudSync,
    );
  }

  @override
  List<Object?> get props => [
        darkMode,
        language,
        notificationsEnabled,
        autoTranscribe,
        defaultCategory,
        maxRecordingDuration,
        autoSave,
        cloudSync,
      ];
}
