import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/app_theme.dart';
import '../widgets/gradient_background.dart';
import '../widgets/loving_messages.dart';
import '../state/always_on_voice_provider.dart';

class AlwaysOnSettingsScreen extends StatefulWidget {
  const AlwaysOnSettingsScreen({super.key});

  @override
  State<AlwaysOnSettingsScreen> createState() => _AlwaysOnSettingsScreenState();
}

class _AlwaysOnSettingsScreenState extends State<AlwaysOnSettingsScreen> {
  @override
  void initState() {
    super.initState();
    
    // Initialize always-on voice provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AlwaysOnVoiceProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return RomanticScaffold(
      appBar: AppBar(
        title: const Text('Always-On Voice Detection 🎯'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Consumer<AlwaysOnVoiceProvider>(
        builder: (context, provider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Status card
                _buildStatusCard(provider),
                
                const SizedBox(height: 24),
                
                // Main toggle
                _buildMainToggle(provider),
                
                const SizedBox(height: 24),
                
                // Settings sections
                if (provider.isAlwaysOnEnabled) ...[
                  _buildPrivacySection(provider),
                  const SizedBox(height: 24),
                  _buildBatterySection(provider),
                  const SizedBox(height: 24),
                  _buildSensitivitySection(provider),
                  const SizedBox(height: 24),
                ],
                
                // Wake words section
                _buildWakeWordsSection(),
                
                const SizedBox(height: 24),
                
                // Information section
                _buildInformationSection(),
                
                // Generated memory preview
                if (provider.generatedMemory != null) ...[
                  const SizedBox(height: 24),
                  _buildMemoryPreview(provider),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusCard(AlwaysOnVoiceProvider provider) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            provider.isAlwaysOnEnabled 
                ? AppColors.lightBlue.withValues(alpha: 0.1)
                : Colors.grey.withValues(alpha: 0.1),
            provider.isAlwaysOnEnabled
                ? AppColors.softBlushPink.withValues(alpha: 0.1)
                : Colors.grey.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: provider.isAlwaysOnEnabled
              ? AppColors.lightBlue.withValues(alpha: 0.2)
              : Colors.grey.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            provider.isAlwaysOnEnabled ? Icons.hearing : Icons.hearing_disabled,
            size: 48,
            color: provider.isAlwaysOnEnabled 
                ? AppColors.lightBlue 
                : Colors.grey,
          ),
          
          const SizedBox(height: 12),
          
          Text(
            provider.currentStatus,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          
          if (provider.isProcessingWakeWord) ...[
            const SizedBox(height: 12),
            const CircularProgressIndicator(),
          ],
        ],
      ),
    );
  }

  Widget _buildMainToggle(AlwaysOnVoiceProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.power_settings_new,
                  color: AppColors.lightBlue,
                ),
                const SizedBox(width: 12),
                Text(
                  'Always-On Detection',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'Enable background voice detection to capture memories hands-free',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            
            const SizedBox(height: 16),
            
            SwitchListTile(
              title: Text(
                provider.isAlwaysOnEnabled ? 'Enabled' : 'Disabled',
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
              subtitle: Text(
                provider.isAlwaysOnEnabled 
                    ? 'Say "Hey Younes" anytime to capture memories'
                    : 'Tap to enable always-on voice detection',
              ),
              value: provider.isAlwaysOnEnabled,
              onChanged: (value) async {
                if (value) {
                  await provider.enableAlwaysOnDetection();
                } else {
                  await provider.disableAlwaysOnDetection();
                }
              },
              activeColor: AppColors.lightBlue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacySection(AlwaysOnVoiceProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.privacy_tip,
                  color: AppColors.softBlushPink,
                ),
                const SizedBox(width: 12),
                Text(
                  'Privacy Settings',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            SwitchListTile(
              title: const Text('On-Device Processing'),
              subtitle: Text(
                'Process voice data locally for maximum privacy\n'
                'Current level: ${provider.getPrivacyLevel()}',
              ),
              value: provider.onDeviceProcessingEnabled,
              onChanged: (value) => provider.setOnDeviceProcessing(value),
              activeColor: AppColors.softBlushPink,
            ),
            
            const SizedBox(height: 8),
            
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.softBlushPink.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 20,
                    color: AppColors.softBlushPink,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Voice data is processed securely and never stored permanently',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBatterySection(AlwaysOnVoiceProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.battery_saver,
                  color: Colors.green,
                ),
                const SizedBox(width: 12),
                Text(
                  'Battery Optimization',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            SwitchListTile(
              title: const Text('Battery Optimization'),
              subtitle: Text(
                'Reduce battery usage with smart listening\n'
                'Estimated usage: ${provider.getBatteryUsageEstimate()}',
              ),
              value: provider.batteryOptimizationEnabled,
              onChanged: (value) => provider.setBatteryOptimization(value),
              activeColor: Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSensitivitySection(AlwaysOnVoiceProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tune,
                  color: AppColors.voiceButtonBlue,
                ),
                const SizedBox(width: 12),
                Text(
                  'Detection Sensitivity',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Text(
              'Sensitivity Level: ${provider.sensitivityLevel}/5',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            Slider(
              value: provider.sensitivityLevel.toDouble(),
              min: 1,
              max: 5,
              divisions: 4,
              label: provider.sensitivityLevel.toString(),
              onChanged: (value) => provider.setSensitivityLevel(value.round()),
              activeColor: AppColors.voiceButtonBlue,
            ),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Low\n(Less sensitive)',
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
                Text(
                  'High\n(More sensitive)',
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWakeWordsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.record_voice_over,
                  color: AppColors.lightBlue,
                ),
                const SizedBox(width: 12),
                Text(
                  'Wake Words',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Text(
              'Say any of these phrases to start recording:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                '"Hey Younes"',
                '"Younes, fkerni..."',
                '"Note to self"',
                '"Younes"',
              ].map((phrase) => Chip(
                label: Text(phrase),
                backgroundColor: AppColors.lightBlue.withValues(alpha: 0.1),
                labelStyle: TextStyle(
                  color: AppColors.textPrimary,
                  fontSize: 12,
                ),
              )).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInformationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 12),
                Text(
                  'How It Works',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            _buildInfoItem(
              '1. Background Listening',
              'EchoMind runs a lightweight service that listens for wake words',
            ),
            
            _buildInfoItem(
              '2. Wake Word Detection',
              'When "Hey Younes" is detected, recording starts automatically',
            ),
            
            _buildInfoItem(
              '3. Voice Recording',
              'Records 10-15 seconds of audio after wake word detection',
            ),
            
            _buildInfoItem(
              '4. AI Processing',
              'Transcribes and creates beautiful memories with AI enhancement',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 6),
            decoration: BoxDecoration(
              color: AppColors.lightBlue,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMemoryPreview(AlwaysOnVoiceProvider provider) {
    final memory = provider.generatedMemory!;
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.lightBlue.withValues(alpha: 0.1),
            AppColors.softBlushPink.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_awesome,
                color: AppColors.lightBlue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Memory from Wake Word',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Text(
            memory.summary ?? 'Untitled Memory',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            memory.originalText,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 12),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Chip(
                label: Text('Wake Word: ${memory.triggerPhrase}'),
                backgroundColor: AppColors.softBlushPink.withValues(alpha: 0.3),
              ),
              
              Row(
                children: [
                  TextButton(
                    onPressed: () => provider.clearMemory(),
                    child: const Text('Clear'),
                  ),
                  
                  ElevatedButton(
                    onPressed: () async {
                      // Save memory logic here
                      provider.clearMemory();
                      
                      if (mounted) {
                        RomanticSnackBar.show(
                          context,
                          'Memory saved! 💙',
                        );
                      }
                    },
                    child: const Text('Save Memory'),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
