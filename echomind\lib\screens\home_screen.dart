import 'package:flutter/material.dart';
import '../core/app_constants.dart';
import '../core/app_utils.dart';
import '../core/app_theme.dart';
import '../widgets/gradient_background.dart';
import '../widgets/romantic_voice_button.dart';
import '../widgets/loving_messages.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return RomanticScaffold(
      animated: true,
      appBar: AppBar(
        title: const Text('EchoMind 💙'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search_rounded),
            onPressed: () => _showSearchDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.settings_rounded),
            onPressed: () => _showSettingsDialog(context),
          ),
        ],
      ),
      body: IndexedStack(
        index: _selectedIndex,
        children: const [_HomeTab(), _MemoriesTab(), _FavoritesTab()],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          gradient: AppTheme.navGradient.scale(0.3),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: BottomNavigationBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          currentIndex: _selectedIndex,
          onTap: (index) => setState(() => _selectedIndex = index),
          selectedItemColor: AppColors.textPrimary,
          unselectedItemColor: AppColors.textSecondary,
          type: BottomNavigationBarType.fixed,
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home_rounded),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.memory_rounded),
              label: 'Memories',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.favorite_rounded),
              label: 'Favorites',
            ),
          ],
        ),
      ),
      floatingActionButton: RomanticVoiceButton(
        isRecording: false,
        onPressed: () => Navigator.pushNamed(context, '/voice-capture'),
        size: 60,
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Memories'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: 'Enter search terms...',
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement search functionality
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Settings'),
        content: const Text('Settings screen coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

class _HomeTab extends StatelessWidget {
  const _HomeTab();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome section with romantic styling
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.pureWhite,
                  AppColors.lightBlue.withValues(alpha: 0.05),
                  AppColors.softBlushPink.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: AppColors.lightBlue.withValues(alpha: 0.1),
                  blurRadius: 20,
                  spreadRadius: 2,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LovingMessages.getWelcomeMessage(),
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w700,
                    letterSpacing: 0.5,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Your digital diary awaits your beautiful thoughts ✨',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () =>
                            Navigator.pushNamed(context, '/voice-capture'),
                        icon: const Icon(Icons.mic_rounded),
                        label: const Text('Start Recording'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Voice Trigger Recording Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () =>
                        Navigator.pushNamed(context, '/voice-recording'),
                    icon: const Icon(Icons.hearing_rounded),
                    label: const Text('Voice Triggers 🎯'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.voiceButtonBlue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Always-On Detection Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () =>
                        Navigator.pushNamed(context, '/always-on-settings'),
                    icon: const Icon(Icons.settings_voice),
                    label: const Text('Always-On Detection ⚡'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.softBlushPink,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),

                Row(
                  children: [
                    const Spacer(),
                    const SizedBox(width: 12),
                    Container(
                      decoration: BoxDecoration(
                        color: AppColors.softBlushPink.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: IconButton(
                        onPressed: () =>
                            Navigator.pushNamed(context, '/memory-timeline'),
                        icon: const Icon(Icons.timeline_rounded),
                        color: AppColors.emotionalPink,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: AppConstants.largePadding),

          // Quick stats
          Text('Quick Stats', style: Theme.of(context).textTheme.titleLarge),
          const SizedBox(height: AppConstants.defaultPadding),

          Row(
            children: [
              Expanded(
                child: _StatCard(
                  title: 'Total Memories',
                  value: '12',
                  icon: Icons.memory,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _StatCard(
                  title: 'This Week',
                  value: '3',
                  icon: Icons.calendar_today,
                  color: Colors.green,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          Row(
            children: [
              Expanded(
                child: _StatCard(
                  title: 'Favorites',
                  value: '5',
                  icon: Icons.favorite,
                  color: Colors.red,
                ),
              ),
              const SizedBox(width: AppConstants.defaultPadding),
              Expanded(
                child: _StatCard(
                  title: 'Categories',
                  value: '4',
                  icon: Icons.category,
                  color: Colors.purple,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.largePadding),

          // Recent memories
          Text(
            'Recent Memories',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.defaultPadding),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 3,
            itemBuilder: (context, index) => _RecentMemoryCard(
              title: 'Memory ${index + 1}',
              content: 'This is a placeholder memory content...',
              category:
                  AppConstants.memoryCategories[index %
                      AppConstants.memoryCategories.length],
              time: DateTime.now().subtract(Duration(hours: index + 1)),
            ),
          ),

          const SizedBox(height: AppConstants.defaultPadding),

          Center(
            child: TextButton(
              onPressed: () => Navigator.pushNamed(context, '/memory-timeline'),
              child: const Text('View All Memories'),
            ),
          ),
        ],
      ),
    );
  }
}

class _MemoriesTab extends StatelessWidget {
  const _MemoriesTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.memory, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('Memories tab coming soon!'),
          SizedBox(height: 8),
          Text('This will show all your memories with filtering options.'),
        ],
      ),
    );
  }
}

class _FavoritesTab extends StatelessWidget {
  const _FavoritesTab();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.favorite, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('Favorites tab coming soon!'),
          SizedBox(height: 8),
          Text('This will show your favorite memories.'),
        ],
      ),
    );
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _RecentMemoryCard extends StatelessWidget {
  final String title;
  final String content;
  final String category;
  final DateTime time;

  const _RecentMemoryCard({
    required this.title,
    required this.content,
    required this.category,
    required this.time,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppUtils.getCategoryColor(category),
          child: Text(
            category[0].toUpperCase(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(AppUtils.truncateText(content, 50)),
            const SizedBox(height: 4),
            Text(
              AppUtils.formatRelativeTime(time),
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          // TODO: Navigate to memory detail
        },
      ),
    );
  }
}
