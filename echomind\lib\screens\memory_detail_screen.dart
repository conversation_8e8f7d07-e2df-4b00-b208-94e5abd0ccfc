import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../core/app_theme.dart';
import '../widgets/gradient_background.dart';
import '../widgets/loving_messages.dart';
import '../models/memory_entry.dart';

class MemoryDetailScreen extends StatelessWidget {
  final MemoryEntry memory;

  const MemoryDetailScreen({
    super.key,
    required this.memory,
  });

  @override
  Widget build(BuildContext context) {
    return RomanticScaffold(
      appBar: AppBar(
        title: const Text('Memory Details'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareMemory(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with mood and date
            _buildHeader(context),
            
            const SizedBox(height: 24),
            
            // Summary section
            _buildSummarySection(context),
            
            const SizedBox(height: 20),
            
            // Original text section
            _buildOriginalTextSection(context),
            
            const SizedBox(height: 20),
            
            // Trigger phrase section
            if (memory.triggerPhrase != null && memory.triggerPhrase!.isNotEmpty)
              _buildTriggerPhraseSection(context),
            
            const SizedBox(height: 20),
            
            // Tags section
            if (memory.tags.isNotEmpty)
              _buildTagsSection(context),
            
            const SizedBox(height: 20),
            
            // Metadata section
            _buildMetadataSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final moodEmoji = _getMoodEmoji(memory.moodTag);
    final formattedDate = DateFormat('EEEE, MMMM d, y').format(memory.createdAt);
    final formattedTime = DateFormat('h:mm a').format(memory.createdAt);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.lightBlue.withValues(alpha: 0.1),
            AppColors.softBlushPink.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.lightBlue.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Text(
            moodEmoji,
            style: const TextStyle(fontSize: 48),
          ),
          
          const SizedBox(height: 12),
          
          Text(
            formattedDate,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 4),
          
          Text(
            formattedTime,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: AppColors.lightBlue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Memory Summary',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Text(
              memory.summary ?? 'No summary available',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w600,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOriginalTextSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.transcribe,
                  color: AppColors.textSecondary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Original Recording',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.pureWhite,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.textSecondary.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                memory.originalText,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  height: 1.5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTriggerPhraseSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.record_voice_over,
                  color: AppColors.voiceButtonBlue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Trigger Phrase',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.voiceButtonBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.voiceButtonBlue.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                '"${memory.triggerPhrase}"',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTagsSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.label,
                  color: AppColors.softBlushPink,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Tags',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: memory.tags.map((tag) => Chip(
                label: Text(tag),
                backgroundColor: AppColors.softBlushPink.withValues(alpha: 0.2),
                labelStyle: TextStyle(
                  color: AppColors.textPrimary,
                  fontSize: 12,
                ),
              )).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.textSecondary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Details',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            _buildMetadataRow(context, 'Storage Mode', memory.storageMode ?? 'cloud'),
            _buildMetadataRow(context, 'Memory ID', memory.id.substring(0, 8)),
            _buildMetadataRow(context, 'Created', DateFormat('MMM d, y h:mm a').format(memory.createdAt)),
            if (memory.updatedAt != memory.createdAt)
              _buildMetadataRow(context, 'Updated', DateFormat('MMM d, y h:mm a').format(memory.updatedAt)),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  String _getMoodEmoji(String? moodTag) {
    const moodEmojis = {
      'happy': '😊',
      'excited': '🎉',
      'grateful': '🙏',
      'thoughtful': '🤔',
      'neutral': '😐',
      'concerned': '😟',
      'urgent': '⚡',
      'reminder': '📝',
      'idea': '💡',
      'goal': '🎯',
    };
    
    return moodEmojis[moodTag] ?? '💭';
  }

  void _shareMemory(BuildContext context) {
    // TODO: Implement memory sharing
    RomanticSnackBar.show(
      context,
      'Memory sharing coming soon! 💙',
    );
  }
}
