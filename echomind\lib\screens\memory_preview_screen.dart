import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/app_theme.dart';
import '../widgets/gradient_background.dart';
import '../widgets/loving_messages.dart';
import '../models/memory_entry.dart';
import '../state/app_state_provider.dart';

class MemoryPreviewScreen extends StatefulWidget {
  final MemoryEntry memory;
  final String triggerPhrase;
  final String transcribedText;
  final String aiSummary;

  const MemoryPreviewScreen({
    super.key,
    required this.memory,
    required this.triggerPhrase,
    required this.transcribedText,
    required this.aiSummary,
  });

  @override
  State<MemoryPreviewScreen> createState() => _MemoryPreviewScreenState();
}

class _MemoryPreviewScreenState extends State<MemoryPreviewScreen> {
  late TextEditingController _summaryController;
  String _selectedMoodTag = 'neutral';
  bool _useCloudStorage = true;
  bool _isSaving = false;

  // Mood options with emojis
  final Map<String, String> _moodOptions = {
    'happy': '😊 Happy',
    'excited': '🎉 Excited',
    'grateful': '🙏 Grateful',
    'thoughtful': '🤔 Thoughtful',
    'neutral': '😐 Neutral',
    'concerned': '😟 Concerned',
    'urgent': '⚡ Urgent',
    'reminder': '📝 Reminder',
    'idea': '💡 Idea',
    'goal': '🎯 Goal',
  };

  @override
  void initState() {
    super.initState();
    _summaryController = TextEditingController(text: widget.aiSummary);
    _selectedMoodTag = widget.memory.moodTag ?? 'neutral';
  }

  @override
  void dispose() {
    _summaryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RomanticScaffold(
      appBar: AppBar(
        title: const Text('Memory Preview'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          if (!_isSaving)
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(),

            const SizedBox(height: 24),

            // Trigger Phrase Section
            _buildTriggerPhraseSection(),

            const SizedBox(height: 20),

            // Transcribed Text Section
            _buildTranscribedTextSection(),

            const SizedBox(height: 20),

            // Editable Summary Section
            _buildEditableSummarySection(),

            const SizedBox(height: 20),

            // Mood Tag Selector
            _buildMoodTagSelector(),

            const SizedBox(height: 20),

            // Storage Consent Toggle
            _buildStorageConsentSection(),

            const SizedBox(height: 32),

            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.lightBlue.withValues(alpha: 0.1),
            AppColors.softBlushPink.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.lightBlue.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(Icons.auto_awesome, size: 48, color: AppColors.lightBlue),

          const SizedBox(height: 12),

          Text(
            'Review Your Memory',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          Text(
            'Review and customize your memory before saving',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTriggerPhraseSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.record_voice_over,
                  color: AppColors.voiceButtonBlue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Trigger Phrase',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.voiceButtonBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.voiceButtonBlue.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                '"${widget.triggerPhrase}"',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTranscribedTextSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.transcribe,
                  color: AppColors.textSecondary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Transcribed Text',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.pureWhite,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.textSecondary.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                widget.transcribedText,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEditableSummarySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.edit, color: AppColors.lightBlue, size: 20),
                const SizedBox(width: 8),
                Text(
                  'AI Summary (Editable)',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            TextField(
              controller: _summaryController,
              maxLines: 3,
              maxLength: 150,
              decoration: InputDecoration(
                hintText: 'Edit your memory summary...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: AppColors.lightBlue.withValues(alpha: 0.05),
              ),
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
            ),

            const SizedBox(height: 8),

            Text(
              'This summary will be used for quick memory recall',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMoodTagSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.mood, color: AppColors.softBlushPink, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Mood Tag (Optional)',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _moodOptions.entries.map((entry) {
                final isSelected = _selectedMoodTag == entry.key;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedMoodTag = entry.key;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.softBlushPink
                          : AppColors.softBlushPink.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected
                            ? AppColors.softBlushPink
                            : AppColors.softBlushPink.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      entry.value,
                      style: TextStyle(
                        color: isSelected
                            ? Colors.white
                            : AppColors.textPrimary,
                        fontWeight: isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                        fontSize: 12,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStorageConsentSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.storage, color: Colors.green, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Storage Preference',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            SwitchListTile(
              title: Text(
                _useCloudStorage ? 'Cloud Storage' : 'Local Storage',
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
              subtitle: Text(
                _useCloudStorage
                    ? 'Sync across devices, backed up securely'
                    : 'Stored locally on this device only',
              ),
              value: _useCloudStorage,
              onChanged: (value) {
                setState(() {
                  _useCloudStorage = value;
                });
              },
              activeColor: AppColors.lightBlue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isSaving ? null : () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ),

        const SizedBox(width: 16),

        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isSaving ? null : _saveMemory,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.lightBlue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: _isSaving
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Save Memory 💙',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
          ),
        ),
      ],
    );
  }

  Future<void> _saveMemory() async {
    if (_summaryController.text.trim().isEmpty) {
      RomanticSnackBar.show(
        context,
        'Please provide a summary for your memory',
        isError: true,
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // Create updated memory with user edits
      final updatedMemory = MemoryEntry.create(
        userId: widget.memory.userId,
        originalText: widget.transcribedText,
        summary: _summaryController.text.trim(),
        moodTag: _selectedMoodTag,
        triggerPhrase: widget.triggerPhrase,
        tags: widget.memory.tags,
        storageMode: _useCloudStorage ? 'cloud' : 'local',
      );

      // Save to Supabase
      final appState = context.read<AppStateProvider>();
      await appState.saveMemory(updatedMemory);

      if (mounted) {
        RomanticSnackBar.show(context, 'Memory saved successfully! 💙');

        // Navigate back to home or timeline
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
    } catch (e) {
      if (mounted) {
        RomanticSnackBar.show(
          context,
          'Failed to save memory: $e',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
