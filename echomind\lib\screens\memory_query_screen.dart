import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../core/app_theme.dart';
import '../widgets/gradient_background.dart';
import '../widgets/loving_messages.dart';
import '../state/app_state_provider.dart';
import '../models/memory_entry.dart';
import 'memory_detail_screen.dart';

class MemoryQueryScreen extends StatefulWidget {
  const MemoryQueryScreen({super.key});

  @override
  State<MemoryQueryScreen> createState() => _MemoryQueryScreenState();
}

class _MemoryQueryScreenState extends State<MemoryQueryScreen> {
  final TextEditingController _queryController = TextEditingController();
  List<MemoryEntry> _searchResults = [];
  bool _isSearching = false;
  bool _hasSearched = false;

  // Mood emoji mapping
  final Map<String, String> _moodEmojis = {
    'happy': '😊',
    'excited': '🎉',
    'grateful': '🙏',
    'thoughtful': '🤔',
    'neutral': '😐',
    'concerned': '😟',
    'urgent': '⚡',
    'reminder': '📝',
    'idea': '💡',
    'goal': '🎯',
  };

  @override
  void dispose() {
    _queryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RomanticScaffold(
      appBar: AppBar(
        title: const Text('Memory Query 🔍'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Search input section
          _buildSearchSection(),

          // Results section
          Expanded(child: _buildResultsSection()),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.lightBlue.withValues(alpha: 0.1),
                  AppColors.softBlushPink.withValues(alpha: 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.lightBlue.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              children: [
                Icon(Icons.search, size: 48, color: AppColors.lightBlue),

                const SizedBox(height: 12),

                Text(
                  'Search Your Memories',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 8),

                Text(
                  'Ask questions about your memories and find what you\'re looking for',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Search input
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _queryController,
                  decoration: InputDecoration(
                    hintText: 'What did I say about tea?',
                    prefixIcon: Icon(Icons.search, color: AppColors.lightBlue),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: AppColors.pureWhite,
                  ),
                  onSubmitted: (_) => _searchMemories(),
                ),
              ),

              const SizedBox(width: 12),

              ElevatedButton(
                onPressed: _isSearching ? null : _searchMemories,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.lightBlue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isSearching
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Icon(Icons.search),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Example queries
          _buildExampleQueries(),
        ],
      ),
    );
  }

  Widget _buildExampleQueries() {
    final examples = [
      'What did I say about tea?',
      'Show me reminders',
      'Find my ideas',
      'What was I grateful for?',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Try asking:',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w600,
          ),
        ),

        const SizedBox(height: 8),

        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: examples
              .map(
                (example) => GestureDetector(
                  onTap: () {
                    _queryController.text = example;
                    _searchMemories();
                  },
                  child: Chip(
                    label: Text(example),
                    backgroundColor: AppColors.lightBlue.withValues(alpha: 0.1),
                    labelStyle: TextStyle(
                      color: AppColors.textPrimary,
                      fontSize: 12,
                    ),
                  ),
                ),
              )
              .toList(),
        ),
      ],
    );
  }

  Widget _buildResultsSection() {
    if (!_hasSearched) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.psychology,
              size: 64,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),

            const SizedBox(height: 16),

            Text(
              'Start searching to find your memories',
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_isSearching) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),

            const SizedBox(height: 16),

            Text(
              'No memories found',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'Try a different search term or create more memories',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Results header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          child: Row(
            children: [
              Icon(Icons.search, color: AppColors.lightBlue, size: 20),

              const SizedBox(width: 8),

              Text(
                'Found ${_searchResults.length} memories',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),

        // Results list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            itemCount: _searchResults.length,
            itemBuilder: (context, index) {
              final memory = _searchResults[index];
              return _buildMemoryCard(memory);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMemoryCard(MemoryEntry memory) {
    final moodEmoji = _moodEmojis[memory.moodTag] ?? '💭';
    final formattedDate = DateFormat('MMM d, y').format(memory.createdAt);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _openMemoryDetail(memory),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with mood and date
              Row(
                children: [
                  Text(moodEmoji, style: const TextStyle(fontSize: 24)),

                  const SizedBox(width: 12),

                  Expanded(
                    child: Text(
                      memory.summary ?? 'Untitled Memory',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  Text(
                    formattedDate,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Memory content preview
              Text(
                memory.originalText,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              // Tags
              if (memory.tags.isNotEmpty) ...[
                const SizedBox(height: 12),
                Wrap(
                  spacing: 6,
                  runSpacing: 4,
                  children: memory.tags
                      .take(3)
                      .map(
                        (tag) => Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.softBlushPink.withValues(
                              alpha: 0.2,
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            tag,
                            style: TextStyle(
                              color: AppColors.textPrimary,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      )
                      .toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _searchMemories() async {
    final query = _queryController.text.trim();
    if (query.isEmpty) return;

    setState(() {
      _isSearching = true;
      _hasSearched = true;
    });

    try {
      final appState = context.read<AppStateProvider>();

      // Ensure memories are loaded first
      await appState.loadMemories();

      // Search through loaded memories
      final allMemories = appState.memories;
      final lowercaseQuery = query.toLowerCase();

      final results = allMemories.where((memory) {
        return memory.summary?.toLowerCase().contains(lowercaseQuery) == true ||
            memory.originalText.toLowerCase().contains(lowercaseQuery) ||
            memory.moodTag?.toLowerCase().contains(lowercaseQuery) == true ||
            memory.triggerPhrase?.toLowerCase().contains(lowercaseQuery) ==
                true ||
            memory.tags.any(
              (tag) => tag.toLowerCase().contains(lowercaseQuery),
            );
      }).toList();

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _isSearching = false;
      });

      if (mounted) {
        RomanticSnackBar.show(context, 'Search failed: $e', isError: true);
      }
    }
  }

  void _openMemoryDetail(MemoryEntry memory) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MemoryDetailScreen(memory: memory),
      ),
    );
  }
}
