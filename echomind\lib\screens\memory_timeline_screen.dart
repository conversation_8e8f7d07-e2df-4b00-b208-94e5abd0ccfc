import 'package:flutter/material.dart';
import '../core/app_constants.dart';
import '../core/app_utils.dart';
import '../models/memory_entry.dart';

class MemoryTimelineScreen extends StatefulWidget {
  const MemoryTimelineScreen({super.key});

  @override
  State<MemoryTimelineScreen> createState() => _MemoryTimelineScreenState();
}

class _MemoryTimelineScreenState extends State<MemoryTimelineScreen> {
  String _searchQuery = '';
  String _selectedCategory = 'All';
  bool _showFavoritesOnly = false;
  List<MemoryEntry> _memories = [];
  List<MemoryEntry> _filteredMemories = [];

  @override
  void initState() {
    super.initState();
    _loadMemories();
  }

  void _loadMemories() {
    // TODO: Load actual memories from service
    _memories = _generatePlaceholderMemories();
    _filterMemories();
  }

  void _filterMemories() {
    setState(() {
      _filteredMemories = _memories.where((memory) {
        // Category filter
        if (_selectedCategory != 'All' && memory.category != _selectedCategory) {
          return false;
        }
        
        // Favorites filter
        if (_showFavoritesOnly && !memory.isFavorite) {
          return false;
        }
        
        // Search filter
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          return memory.title.toLowerCase().contains(query) ||
                 memory.content.toLowerCase().contains(query) ||
                 memory.tags.any((tag) => tag.toLowerCase().contains(query));
        }
        
        return true;
      }).toList();
      
      // Sort by creation date (newest first)
      _filteredMemories.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Memory Timeline'),
        actions: [
          IconButton(
            icon: Icon(
              _showFavoritesOnly ? Icons.favorite : Icons.favorite_border,
              color: _showFavoritesOnly ? Colors.red : null,
            ),
            onPressed: () {
              setState(() => _showFavoritesOnly = !_showFavoritesOnly);
              _filterMemories();
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'sort_date') {
                // TODO: Implement date sorting
              } else if (value == 'sort_category') {
                // TODO: Implement category sorting
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'sort_date',
                child: Text('Sort by Date'),
              ),
              const PopupMenuItem(
                value: 'sort_category',
                child: Text('Sort by Category'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter section
          _buildSearchAndFilter(),
          
          // Memories list
          Expanded(
            child: _filteredMemories.isEmpty
                ? _buildEmptyState()
                : _buildMemoriesList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.pushNamed(context, '/voice-capture'),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Card(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // Search field
            TextField(
              decoration: const InputDecoration(
                hintText: 'Search memories...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                _searchQuery = value;
                _filterMemories();
              },
            ),
            
            const SizedBox(height: AppConstants.defaultPadding),
            
            // Category filter
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(),
              ),
              items: ['All', ...AppConstants.memoryCategories].map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Text(category),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedCategory = value);
                  _filterMemories();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.memory,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory != 'All' || _showFavoritesOnly
                ? 'No memories found'
                : 'No memories yet',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory != 'All' || _showFavoritesOnly
                ? 'Try adjusting your filters'
                : 'Start by recording your first memory',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: () => Navigator.pushNamed(context, '/voice-capture'),
            icon: const Icon(Icons.mic),
            label: const Text('Record Memory'),
          ),
        ],
      ),
    );
  }

  Widget _buildMemoriesList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      itemCount: _filteredMemories.length,
      itemBuilder: (context, index) {
        final memory = _filteredMemories[index];
        return _MemoryCard(
          memory: memory,
          onTap: () => _showMemoryDetail(memory),
          onFavoriteToggle: () => _toggleFavorite(memory),
          onDelete: () => _deleteMemory(memory),
        );
      },
    );
  }

  void _showMemoryDetail(MemoryEntry memory) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => _MemoryDetailSheet(memory: memory),
    );
  }

  void _toggleFavorite(MemoryEntry memory) {
    // TODO: Implement favorite toggle
    AppUtils.showSnackBar(
      context,
      memory.isFavorite ? 'Removed from favorites' : 'Added to favorites',
    );
  }

  void _deleteMemory(MemoryEntry memory) async {
    final confirmed = await AppUtils.showConfirmDialog(
      context,
      'Delete Memory',
      'Are you sure you want to delete "${memory.title}"?',
    );
    
    if (confirmed == true) {
      // TODO: Implement memory deletion
      AppUtils.showSnackBar(context, AppConstants.memoryDeleted);
      _loadMemories(); // Refresh the list
    }
  }

  List<MemoryEntry> _generatePlaceholderMemories() {
    return [
      MemoryEntry.create(
        title: 'Meeting Notes',
        content: 'Discussed project timeline and budget allocation for Q2. Need to follow up with stakeholders.',
        category: 'Work',
        tags: ['meeting', 'project', 'budget'],
        isFavorite: true,
      ),
      MemoryEntry.create(
        title: 'Grocery List',
        content: 'Need to buy milk, eggs, bread, vegetables, and fruits for the week.',
        category: 'Personal',
        tags: ['shopping', 'groceries'],
      ),
      MemoryEntry.create(
        title: 'App Idea',
        content: 'Voice-to-text app with AI summarization and smart categorization. Could be useful for students and professionals.',
        category: 'Ideas',
        tags: ['app', 'ai', 'voice'],
        isFavorite: true,
      ),
      MemoryEntry.create(
        title: 'Book Recommendation',
        content: 'Friend recommended "Atomic Habits" by James Clear. Should add to reading list.',
        category: 'Learning',
        tags: ['book', 'habits', 'recommendation'],
      ),
      MemoryEntry.create(
        title: 'Weekend Plans',
        content: 'Visit the museum, have lunch with family, and work on personal project.',
        category: 'Personal',
        tags: ['weekend', 'family', 'plans'],
      ),
    ];
  }
}

class _MemoryCard extends StatelessWidget {
  final MemoryEntry memory;
  final VoidCallback onTap;
  final VoidCallback onFavoriteToggle;
  final VoidCallback onDelete;

  const _MemoryCard({
    required this.memory,
    required this.onTap,
    required this.onFavoriteToggle,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: AppUtils.getCategoryColor(memory.category),
                    child: Text(
                      memory.category[0].toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Expanded(
                    child: Text(
                      memory.title,
                      style: Theme.of(context).textTheme.titleMedium,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      memory.isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: memory.isFavorite ? Colors.red : null,
                    ),
                    onPressed: onFavoriteToggle,
                  ),
                  PopupMenuButton(
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Text('Edit'),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Text('Delete'),
                      ),
                    ],
                    onSelected: (value) {
                      if (value == 'delete') {
                        onDelete();
                      }
                    },
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.smallPadding),
              
              // Content
              Text(
                AppUtils.truncateText(memory.content, 100),
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              
              const SizedBox(height: AppConstants.smallPadding),
              
              // Tags and timestamp
              Row(
                children: [
                  if (memory.hasTags) ...[
                    Expanded(
                      child: Wrap(
                        spacing: 4,
                        children: memory.tags.take(3).map((tag) {
                          return Chip(
                            label: Text(tag),
                            labelStyle: Theme.of(context).textTheme.bodySmall,
                            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                          );
                        }).toList(),
                      ),
                    ),
                  ] else
                    const Spacer(),
                  Text(
                    AppUtils.formatRelativeTime(memory.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _MemoryDetailSheet extends StatelessWidget {
  final MemoryEntry memory;

  const _MemoryDetailSheet({required this.memory});

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      maxChildSize: 0.9,
      minChildSize: 0.5,
      builder: (context, scrollController) {
        return Container(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Title
              Text(
                memory.title,
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              
              const SizedBox(height: AppConstants.smallPadding),
              
              // Metadata
              Row(
                children: [
                  Chip(
                    label: Text(memory.category),
                    backgroundColor: AppUtils.getCategoryColor(memory.category),
                    labelStyle: const TextStyle(color: Colors.white),
                  ),
                  const SizedBox(width: AppConstants.smallPadding),
                  Text(
                    AppUtils.formatDateTime(memory.createdAt),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              
              const SizedBox(height: AppConstants.defaultPadding),
              
              // Content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Text(
                    memory.content,
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ),
              ),
              
              // Tags
              if (memory.hasTags) ...[
                const SizedBox(height: AppConstants.defaultPadding),
                Wrap(
                  spacing: 8,
                  children: memory.tags.map((tag) {
                    return Chip(label: Text(tag));
                  }).toList(),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}
