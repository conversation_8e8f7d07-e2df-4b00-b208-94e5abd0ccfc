import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../core/app_theme.dart';
import '../widgets/gradient_background.dart';
import '../widgets/loving_messages.dart';
import '../state/app_state_provider.dart';
import '../models/memory_entry.dart';
import 'memory_detail_screen.dart';
import 'memory_query_screen.dart';

class MemoryTimelineScreen extends StatefulWidget {
  const MemoryTimelineScreen({super.key});

  @override
  State<MemoryTimelineScreen> createState() => _MemoryTimelineScreenState();
}

class _MemoryTimelineScreenState extends State<MemoryTimelineScreen> {
  String _searchQuery = '';
  bool _isLoading = true;
  List<MemoryEntry> _memories = [];
  List<MemoryEntry> _filteredMemories = [];

  // Mood emoji mapping
  final Map<String, String> _moodEmojis = {
    'happy': '😊',
    'excited': '🎉',
    'grateful': '🙏',
    'thoughtful': '🤔',
    'neutral': '😐',
    'concerned': '😟',
    'urgent': '⚡',
    'reminder': '📝',
    'idea': '💡',
    'goal': '🎯',
  };

  @override
  void initState() {
    super.initState();
    _loadMemories();
  }

  Future<void> _loadMemories() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final appState = context.read<AppStateProvider>();
      await appState.loadMemories();

      setState(() {
        _memories = appState.memories;
        _filteredMemories = _memories;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        RomanticSnackBar.show(
          context,
          'Failed to load memories: $e',
          isError: true,
        );
      }
    }
  }

  void _filterMemories() {
    setState(() {
      _filteredMemories = _memories.where((memory) {
        // Search filter
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          return (memory.summary?.toLowerCase().contains(query) ?? false) ||
              memory.originalText.toLowerCase().contains(query) ||
              (memory.moodTag?.toLowerCase().contains(query) ?? false) ||
              (memory.triggerPhrase?.toLowerCase().contains(query) ?? false) ||
              memory.tags.any((tag) => tag.toLowerCase().contains(query));
        }

        return true;
      }).toList();

      // Sort by creation date (newest first)
      _filteredMemories.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    });
  }

  @override
  Widget build(BuildContext context) {
    return RomanticScaffold(
      appBar: AppBar(
        title: const Text('Memory Timeline 📚'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MemoryQueryScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Search section
                _buildSearchSection(),

                // Memories list
                Expanded(
                  child: _filteredMemories.isEmpty
                      ? _buildEmptyState()
                      : _buildMemoriesList(),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.pushNamed(context, '/voice-capture'),
        backgroundColor: AppColors.lightBlue,
        child: const Icon(Icons.mic),
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Search your memories...',
          prefixIcon: Icon(Icons.search, color: AppColors.lightBlue),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          filled: true,
          fillColor: AppColors.pureWhite,
        ),
        onChanged: (value) {
          setState(() => _searchQuery = value);
          _filterMemories();
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.auto_stories,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),

          const SizedBox(height: 16),

          Text(
            _searchQuery.isNotEmpty ? 'No memories found' : 'No memories yet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          Text(
            _searchQuery.isNotEmpty
                ? 'Try a different search term'
                : 'Start by recording your first memory',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),

          if (_searchQuery.isEmpty) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => Navigator.pushNamed(context, '/voice-capture'),
              icon: const Icon(Icons.mic),
              label: const Text('Record Memory'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.lightBlue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMemoriesList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      itemCount: _filteredMemories.length,
      itemBuilder: (context, index) {
        final memory = _filteredMemories[index];
        return _buildMemoryCard(memory);
      },
    );
  }

  Widget _buildMemoryCard(MemoryEntry memory) {
    final moodEmoji = _moodEmojis[memory.moodTag] ?? '💭';
    final formattedDate = DateFormat('MMM d, y').format(memory.createdAt);
    final formattedTime = DateFormat('h:mm a').format(memory.createdAt);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: InkWell(
        onTap: () => _openMemoryDetail(memory),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with mood, summary, and date
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(moodEmoji, style: const TextStyle(fontSize: 32)),

                  const SizedBox(width: 12),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          memory.summary ?? 'Untitled Memory',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 4),

                        Row(
                          children: [
                            Text(
                              formattedDate,
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: AppColors.textSecondary,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),

                            Text(
                              ' • $formattedTime',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: AppColors.textSecondary),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  Icon(Icons.chevron_right, color: AppColors.textSecondary),
                ],
              ),

              const SizedBox(height: 12),

              // Memory content preview
              Text(
                memory.originalText,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              // Tags and trigger phrase
              if (memory.tags.isNotEmpty ||
                  (memory.triggerPhrase?.isNotEmpty ?? false)) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    // Tags
                    if (memory.tags.isNotEmpty)
                      Expanded(
                        child: Wrap(
                          spacing: 6,
                          runSpacing: 4,
                          children: memory.tags
                              .take(3)
                              .map(
                                (tag) => Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.softBlushPink.withValues(
                                      alpha: 0.2,
                                    ),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    tag,
                                    style: TextStyle(
                                      color: AppColors.textPrimary,
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              )
                              .toList(),
                        ),
                      ),

                    // Trigger phrase indicator
                    if (memory.triggerPhrase?.isNotEmpty ?? false)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.voiceButtonBlue.withValues(
                            alpha: 0.2,
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.record_voice_over,
                              size: 12,
                              color: AppColors.voiceButtonBlue,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Voice',
                              style: TextStyle(
                                color: AppColors.voiceButtonBlue,
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _openMemoryDetail(MemoryEntry memory) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MemoryDetailScreen(memory: memory),
      ),
    );
  }
}
