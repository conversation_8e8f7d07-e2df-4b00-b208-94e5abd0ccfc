import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/app_theme.dart';
import '../widgets/gradient_background.dart';
import '../widgets/loving_messages.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      emoji: '💙',
      title: 'Welcome to EchoMind',
      subtitle: 'Your Personal Memory Companion',
      description: 'EchoMind helps you capture, enhance, and rediscover your thoughts through the power of voice and AI.',
      features: [
        'Voice-activated memory capture',
        'AI-enhanced summaries',
        'Beautiful memory timeline',
        'Natural language search',
      ],
    ),
    OnboardingPage(
      emoji: '🎤',
      title: 'Just Say the Magic Words',
      subtitle: '"<PERSON><PERSON>, f<PERSON><PERSON>..." and <PERSON> will listen',
      description: 'Simply speak your trigger phrase followed by what you want to remember. EchoMind listens only when you ask.',
      features: [
        '"<PERSON><PERSON>, f<PERSON><PERSON> to call mom"',
        '"Hey <PERSON>nes, remind me about tea"',
        '"Note to self: buy groceries"',
        'Works even when app is closed',
      ],
    ),
    OnboardingPage(
      emoji: '🤖',
      title: 'AI-Powered Intelligence',
      subtitle: 'Your thoughts, beautifully enhanced',
      description: 'Our AI transforms your voice into concise, searchable memories with mood detection and smart categorization.',
      features: [
        'Automatic mood analysis',
        'Concise summaries',
        'Smart tagging',
        'Emotional insights',
      ],
    ),
    OnboardingPage(
      emoji: '🔒',
      title: 'Privacy First',
      subtitle: 'Only when you ask. Only for you.',
      description: 'EchoMind respects your privacy. Choose between cloud sync or local-only storage. Your memories, your choice.',
      features: [
        'Local-only mode available',
        'Encrypted cloud storage',
        'No background listening',
        'You control your data',
      ],
    ),
    OnboardingPage(
      emoji: '🌟',
      title: 'Ready to Begin?',
      subtitle: 'Start your memory journey',
      description: 'Create beautiful memories, track your progress, and rediscover your thoughts with EchoMind.',
      features: [
        'Memory streak tracking',
        'XP and achievements',
        'Beautiful timeline view',
        'Natural search',
      ],
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RomanticScaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextButton(
                  onPressed: _completeOnboarding,
                  child: Text(
                    'Skip',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
            
            // Page content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return _buildPage(_pages[index]);
                },
              ),
            ),
            
            // Page indicators
            _buildPageIndicators(),
            
            const SizedBox(height: 20),
            
            // Navigation buttons
            _buildNavigationButtons(),
            
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildPage(OnboardingPage page) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Emoji
          Text(
            page.emoji,
            style: const TextStyle(fontSize: 80),
          ),
          
          const SizedBox(height: 32),
          
          // Title
          Text(
            page.title,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 12),
          
          // Subtitle
          Text(
            page.subtitle,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.lightBlue,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 24),
          
          // Description
          Text(
            page.description,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppColors.textSecondary,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 32),
          
          // Features
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColors.lightBlue.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.lightBlue.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              children: page.features.map((feature) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: AppColors.lightBlue,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        feature,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              )).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        _pages.length,
        (index) => Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: _currentPage == index ? 24 : 8,
          height: 8,
          decoration: BoxDecoration(
            color: _currentPage == index
                ? AppColors.lightBlue
                : AppColors.lightBlue.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Row(
        children: [
          // Previous button
          if (_currentPage > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousPage,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(color: AppColors.lightBlue),
                ),
                child: Text(
                  'Previous',
                  style: TextStyle(color: AppColors.lightBlue),
                ),
              ),
            )
          else
            const Expanded(child: SizedBox()),
          
          const SizedBox(width: 16),
          
          // Next/Get Started button
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _currentPage == _pages.length - 1
                  ? _completeOnboarding
                  : _nextPage,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.lightBlue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                _currentPage == _pages.length - 1 ? 'Get Started 💙' : 'Next',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _completeOnboarding() async {
    // Mark onboarding as completed
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('onboarding_completed', true);
    
    // Navigate to home
    if (mounted) {
      Navigator.pushReplacementNamed(context, '/');
    }
  }
}

class OnboardingPage {
  final String emoji;
  final String title;
  final String subtitle;
  final String description;
  final List<String> features;

  const OnboardingPage({
    required this.emoji,
    required this.title,
    required this.subtitle,
    required this.description,
    required this.features,
  });
}
