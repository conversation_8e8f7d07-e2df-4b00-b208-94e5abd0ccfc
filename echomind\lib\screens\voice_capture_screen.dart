import 'package:flutter/material.dart';
import 'dart:async';
import '../core/app_constants.dart';
import '../core/app_utils.dart';
import '../services/gemini_service.dart';
import '../models/memory_entry.dart';
import 'memory_preview_screen.dart';

class VoiceCaptureScreen extends StatefulWidget {
  const VoiceCaptureScreen({super.key});

  @override
  State<VoiceCaptureScreen> createState() => _VoiceCaptureScreenState();
}

class _VoiceCaptureScreenState extends State<VoiceCaptureScreen>
    with TickerProviderStateMixin {
  final GeminiService _geminiService = GeminiService();

  bool _isRecording = false;
  bool _isProcessing = false;
  Duration _recordingDuration = Duration.zero;
  Timer? _recordingTimer;
  String _transcription = '';
  String _aiSummary = '';
  String _selectedCategory = AppConstants.memoryCategories[0];

  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _waveController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _recordingTimer?.cancel();
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Voice Capture'),
        actions: [
          if (_transcription.isNotEmpty)
            IconButton(icon: const Icon(Icons.save), onPressed: _saveMemory),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          children: [
            // Recording status
            _buildRecordingStatus(),

            const SizedBox(height: AppConstants.largePadding),

            // Recording button
            Expanded(child: Center(child: _buildRecordingButton())),

            const SizedBox(height: AppConstants.largePadding),

            // Transcription section
            if (_transcription.isNotEmpty) ...[
              _buildTranscriptionSection(),
              const SizedBox(height: AppConstants.defaultPadding),
            ],

            // AI Summary section
            if (_aiSummary.isNotEmpty) ...[
              _buildAISummarySection(),
              const SizedBox(height: AppConstants.defaultPadding),
            ],

            // Category selection
            _buildCategorySelection(),

            const SizedBox(height: AppConstants.defaultPadding),

            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordingStatus() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Column(
              children: [
                Icon(
                  _isRecording ? Icons.mic : Icons.mic_off,
                  color: _isRecording ? Colors.red : Colors.grey,
                ),
                const SizedBox(height: 4),
                Text(
                  _isRecording ? 'Recording' : 'Ready',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            Column(
              children: [
                const Icon(Icons.timer),
                const SizedBox(height: 4),
                Text(
                  AppUtils.formatDuration(_recordingDuration),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            Column(
              children: [
                Icon(
                  _isProcessing ? Icons.sync : Icons.check_circle,
                  color: _isProcessing ? Colors.orange : Colors.green,
                ),
                const SizedBox(height: 4),
                Text(
                  _isProcessing ? 'Processing' : 'Ready',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordingButton() {
    return GestureDetector(
      onTap: _isProcessing ? null : _toggleRecording,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _isRecording ? _pulseAnimation.value : 1.0,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _isRecording
                    ? Colors.red
                    : Theme.of(context).primaryColor,
                boxShadow: [
                  BoxShadow(
                    color:
                        (_isRecording
                                ? Colors.red
                                : Theme.of(context).primaryColor)
                            .withValues(alpha: 0.3),
                    blurRadius: 20,
                    spreadRadius: _isRecording ? 10 : 5,
                  ),
                ],
              ),
              child: Icon(
                _isRecording ? Icons.stop : Icons.mic,
                size: 48,
                color: Colors.white,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTranscriptionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Transcription',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(
                  color: Theme.of(
                    context,
                  ).colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                _transcription,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAISummarySection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  size: 20,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'AI Summary',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                color: Theme.of(
                  context,
                ).colorScheme.primaryContainer.withValues(alpha: 0.3),
                border: Border.all(
                  color: Theme.of(
                    context,
                  ).colorScheme.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                _aiSummary,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              'This concise summary was generated by AI to help you remember the key points.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Category', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: AppConstants.smallPadding),
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              items: AppConstants.memoryCategories.map((category) {
                return DropdownMenuItem(value: category, child: Text(category));
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedCategory = value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _clearRecording,
            child: const Text('Clear'),
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        Expanded(
          child: ElevatedButton(
            onPressed: _transcription.isNotEmpty ? _saveMemory : null,
            child: const Text('Save Memory'),
          ),
        ),
      ],
    );
  }

  void _toggleRecording() async {
    if (_isRecording) {
      await _stopRecording();
    } else {
      await _startRecording();
    }
  }

  Future<void> _startRecording() async {
    setState(() {
      _isRecording = true;
      _recordingDuration = Duration.zero;
    });

    // Start pulse animation
    _pulseController.repeat(reverse: true);

    // Start recording timer
    _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _recordingDuration = Duration(seconds: timer.tick);
      });
    });

    // TODO: Start actual audio recording
    print('Started recording...');
  }

  Future<void> _stopRecording() async {
    setState(() {
      _isRecording = false;
      _isProcessing = true;
    });

    // Stop animations and timer
    _pulseController.stop();
    _recordingTimer?.cancel();

    // TODO: Stop actual audio recording and get file path
    print('Stopped recording...');

    // Simulate transcription process
    await Future.delayed(const Duration(seconds: 2));

    // Simulate getting transcription from Whisper API
    final simulatedTranscription =
        'Younes, remind me to check my email for the class schedule and message Salma about the project meeting tomorrow.';

    try {
      // Use AI to create a concise summary
      final summary = await _geminiService.summarizeMemory(
        simulatedTranscription,
      );

      setState(() {
        _isProcessing = false;
        _transcription = simulatedTranscription;
        _aiSummary = summary;
      });
    } catch (e) {
      // Fallback if AI fails
      setState(() {
        _isProcessing = false;
        _transcription = simulatedTranscription;
        _aiSummary =
            'Check email for class schedule; message Salma about project meeting';
      });
    }

    // Transcription and AI summarization completed
  }

  void _clearRecording() {
    setState(() {
      _transcription = '';
      _aiSummary = '';
      _recordingDuration = Duration.zero;
      _selectedCategory = AppConstants.memoryCategories[0];
    });
  }

  void _saveMemory() {
    if (_transcription.isEmpty) return;

    // Create memory entry for preview
    final memory = MemoryEntry.create(
      userId: 'current-user',
      originalText: _transcription,
      summary: _aiSummary.isNotEmpty
          ? _aiSummary
          : _transcription.split(' ').take(8).join(' '),
      moodTag: 'neutral',
      triggerPhrase: 'manual recording',
    );

    // Navigate to memory preview screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MemoryPreviewScreen(
          memory: memory,
          triggerPhrase: 'manual recording',
          transcribedText: _transcription,
          aiSummary: _aiSummary,
        ),
      ),
    );
  }
}
