import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'wake_word_service.dart';

/// Manages background service for always-on voice detection
class BackgroundServiceManager {
  static final BackgroundServiceManager _instance =
      BackgroundServiceManager._internal();
  factory BackgroundServiceManager() => _instance;
  BackgroundServiceManager._internal();

  static const String _serviceChannelId = 'echomind_background_service';
  static const String _wakeWordChannelId = 'echomind_wake_word';

  bool _isServiceRunning = false;

  /// Initialize background service
  Future<void> initializeService() async {
    final service = FlutterBackgroundService();

    // Configure notification channels
    await _configureNotificationChannels();

    // Configure background service
    await service.configure(
      androidConfiguration: AndroidConfiguration(
        onStart: onStart,
        autoStart: false,
        isForegroundMode: true,
        notificationChannelId: _serviceChannelId,
        initialNotificationTitle: 'EchoMind Voice Detection',
        initialNotificationContent: 'Listening for "Hey Younes"...',
        foregroundServiceNotificationId: 888,
      ),
      iosConfiguration: IosConfiguration(
        autoStart: false,
        onForeground: onStart,
        onBackground: onIosBackground,
      ),
    );
  }

  /// Start background voice detection service
  Future<void> startBackgroundService() async {
    if (_isServiceRunning) return;

    final service = FlutterBackgroundService();
    final isRunning = await service.isRunning();

    if (!isRunning) {
      await service.startService();
      _isServiceRunning = true;

      // Save state
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('background_service_enabled', true);
    }
  }

  /// Stop background voice detection service
  Future<void> stopBackgroundService() async {
    final service = FlutterBackgroundService();
    service.invoke('stop_service');
    _isServiceRunning = false;

    // Save state
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('background_service_enabled', false);
  }

  /// Check if background service is running
  Future<bool> isServiceRunning() async {
    final service = FlutterBackgroundService();
    return await service.isRunning();
  }

  /// Send wake word detected event to main app
  static void sendWakeWordDetected(String command) {
    final service = FlutterBackgroundService();
    service.invoke('wake_word_detected', {'command': command});
  }

  /// Configure notification channels
  Future<void> _configureNotificationChannels() async {
    final notifications = FlutterLocalNotificationsPlugin();

    // Background service channel
    const serviceChannel = AndroidNotificationChannel(
      _serviceChannelId,
      'Background Voice Detection',
      description: 'Always-on voice trigger detection service',
      importance: Importance.low,
      playSound: false,
      enableVibration: false,
      showBadge: false,
    );

    // Wake word detection channel
    const wakeWordChannel = AndroidNotificationChannel(
      _wakeWordChannelId,
      'Wake Word Detection',
      description: 'Notifications when wake words are detected',
      importance: Importance.high,
      playSound: true,
      enableVibration: true,
    );

    await notifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(serviceChannel);

    await notifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(wakeWordChannel);
  }
}

/// Background service entry point
@pragma('vm:entry-point')
void onStart(ServiceInstance service) async {
  DartPluginRegistrant.ensureInitialized();

  final wakeWordService = WakeWordService();
  bool isListening = false;

  // Initialize wake word service
  await wakeWordService.initialize();

  // Listen for wake word detection
  wakeWordService.wakeWordDetectedStream.listen((command) {
    // Send to main app
    service.invoke('wake_word_detected', {'command': command});

    // Show notification
    _showWakeWordNotification(command);

    // Launch main app
    _launchMainApp(command);
  });

  // Listen for service commands
  service.on('start_listening').listen((event) async {
    if (!isListening) {
      await wakeWordService.enableAlwaysOnListening();
      isListening = true;
      service.invoke('listening_started');
    }
  });

  service.on('stop_listening').listen((event) async {
    if (isListening) {
      await wakeWordService.disableAlwaysOnListening();
      isListening = false;
      service.invoke('listening_stopped');
    }
  });

  service.on('stop_service').listen((event) async {
    await wakeWordService.disableAlwaysOnListening();
    wakeWordService.dispose();
    service.stopSelf();
  });

  // Auto-start listening if enabled
  final prefs = await SharedPreferences.getInstance();
  final autoStart = prefs.getBool('always_on_listening') ?? false;
  if (autoStart) {
    await wakeWordService.enableAlwaysOnListening();
    isListening = true;
  }

  // Update notification periodically
  Timer.periodic(const Duration(minutes: 1), (timer) {
    // Check if service should continue running
    _updateServiceNotification(isListening);
  });
}

/// iOS background handler
@pragma('vm:entry-point')
Future<bool> onIosBackground(ServiceInstance service) async {
  WidgetsFlutterBinding.ensureInitialized();
  DartPluginRegistrant.ensureInitialized();

  // iOS background processing is limited
  // Implement minimal wake word detection here
  return true;
}

/// Show wake word detected notification
void _showWakeWordNotification(String command) async {
  final notifications = FlutterLocalNotificationsPlugin();

  const androidDetails = AndroidNotificationDetails(
    'echomind_wake_word',
    'Wake Word Detection',
    channelDescription: 'Notifications when wake words are detected',
    importance: Importance.high,
    priority: Priority.high,
    icon: '@mipmap/ic_launcher',
    color: Color(0xFFB3D9FF),
    playSound: true,
    enableVibration: true,
  );

  const iosDetails = DarwinNotificationDetails(
    presentAlert: true,
    presentBadge: true,
    presentSound: true,
  );

  const details = NotificationDetails(android: androidDetails, iOS: iosDetails);

  await notifications.show(
    DateTime.now().millisecondsSinceEpoch ~/ 1000,
    'Hey Younes! 🎯',
    'Voice command detected: "$command"',
    details,
    payload: 'wake_word:$command',
  );
}

/// Launch main app with wake word command
void _launchMainApp(String command) {
  // This would typically use platform channels to launch the main app
  // For now, we'll use the service invoke mechanism
  final service = FlutterBackgroundService();
  service.invoke('launch_app', {
    'command': command,
    'timestamp': DateTime.now().toIso8601String(),
  });
}

/// Update service notification
void _updateServiceNotification(bool isListening) async {
  final notifications = FlutterLocalNotificationsPlugin();

  final title = isListening ? 'EchoMind Listening 👂' : 'EchoMind Ready';

  final content = isListening
      ? 'Say "Hey Younes" to capture a memory'
      : 'Tap to enable voice detection';

  const androidDetails = AndroidNotificationDetails(
    'echomind_background_service',
    'Background Voice Detection',
    channelDescription: 'Always-on voice trigger detection service',
    importance: Importance.low,
    priority: Priority.low,
    ongoing: true,
    autoCancel: false,
    icon: '@mipmap/ic_launcher',
    color: Color(0xFFB3D9FF),
    playSound: false,
    enableVibration: false,
  );

  const iosDetails = DarwinNotificationDetails(
    presentAlert: false,
    presentBadge: false,
    presentSound: false,
  );

  const details = NotificationDetails(android: androidDetails, iOS: iosDetails);

  await notifications.show(
    888, // Same ID as foreground service
    title,
    content,
    details,
  );
}
