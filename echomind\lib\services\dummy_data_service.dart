import '../models/memory_entry.dart';

/// Service for generating dummy data to simulate memory history
class DummyDataService {
  /// Create a memory entry with a custom date
  static MemoryEntry _createMemoryWithDate({
    required String userId,
    required String originalText,
    String? summary,
    String? moodTag,
    String? triggerPhrase,
    String storageMode = 'cloud',
    String? audioFilePath,
    List<String> tags = const [],
    bool isFavorite = false,
    double? audioDuration,
    Map<String, dynamic>? metadata,
    required DateTime createdAt,
  }) {
    return MemoryEntry(
      id: '${createdAt.millisecondsSinceEpoch}_${userId.hashCode}',
      userId: userId,
      summary: summary,
      originalText: originalText,
      createdAt: createdAt,
      updatedAt: createdAt,
      moodTag: moodTag,
      triggerPhrase: triggerPhrase,
      storageMode: storageMode,
      audioFilePath: audioFilePath,
      tags: tags,
      isFavorite: isFavorite,
      audioDuration: audioDuration,
      metadata: metadata ?? {},
    );
  }

  /// Generate dummy memories for demonstration
  static List<MemoryEntry> generateDummyMemories() {
    final now = DateTime.now();

    return [
      // Recent memories (last 7 days)
      _createMemoryWithDate(
        userId: 'demo-user',
        originalText:
            'Younes, fkerni to call mom about dinner plans for Sunday and ask about her doctor appointment.',
        summary: 'Call mom about Sunday dinner and doctor appointment',
        moodTag: 'concerned',
        triggerPhrase: 'Younes, fkerni',
        tags: ['family', 'dinner', 'health'],
        createdAt: now.subtract(const Duration(hours: 2)),
      ),

      _createMemoryWithDate(
        userId: 'demo-user',
        originalText:
            'Hey Younes, I just had the most amazing tea at this new café downtown.',
        summary: 'Amazing jasmine green tea at new downtown café',
        moodTag: 'happy',
        triggerPhrase: 'Hey Younes',
        tags: ['tea', 'café', 'downtown'],
        createdAt: now.subtract(const Duration(days: 1)),
      ),

      _createMemoryWithDate(
        userId: 'demo-user',
        originalText:
            'Note to self: submit the project proposal before 5 PM tomorrow.',
        summary: 'Submit project proposal before 5 PM',
        moodTag: 'stressed',
        triggerPhrase: 'Note to self',
        tags: ['work', 'deadline', 'presentation'],
        createdAt: now.subtract(const Duration(days: 2)),
      ),

      _createMemoryWithDate(
        userId: 'demo-user',
        originalText: 'Younes, remind me that I want to start learning guitar.',
        summary: 'Start learning guitar',
        moodTag: 'motivated',
        triggerPhrase: 'Younes, remind me',
        tags: ['guitar', 'hobby', 'music'],
        createdAt: now.subtract(const Duration(days: 3)),
      ),

      _createMemoryWithDate(
        userId: 'demo-user',
        originalText: 'I had such a peaceful morning walk in the park today.',
        summary: 'Peaceful morning walk in park',
        moodTag: 'peaceful',
        triggerPhrase: 'voice input',
        tags: ['walk', 'park', 'morning', 'gratitude'],
        createdAt: now.subtract(const Duration(days: 4)),
      ),
    ];
  }

  /// Generate memories for a specific mood (for testing)
  static List<MemoryEntry> generateMemoriesForMood(String mood, int count) {
    final now = DateTime.now();
    final memories = <MemoryEntry>[];

    final moodTemplates = {
      'happy': [
        'I feel so joyful today! Everything seems bright and wonderful.',
        'Had an amazing time with friends. Laughter really is the best medicine.',
        'Accomplished my goal today and I\'m celebrating this victory!',
      ],
      'stressed': [
        'Feeling overwhelmed with all these deadlines approaching.',
        'Too much pressure at work and not enough time to handle everything.',
        'Anxious about the presentation tomorrow. Need to prepare more.',
      ],
      'peaceful': [
        'Such a calm and serene morning. I feel completely at peace.',
        'Meditation session was perfect today. Mind feels clear and centered.',
        'Quiet evening with a good book and tea. Pure contentment.',
      ],
      'reflective': [
        'Been thinking deeply about life choices and future directions.',
        'Contemplating the meaning of recent events and their impact.',
        'Reflecting on past experiences and lessons learned.',
      ],
    };

    final templates = moodTemplates[mood] ?? ['Neutral memory entry.'];

    for (int i = 0; i < count; i++) {
      final template = templates[i % templates.length];
      memories.add(
        _createMemoryWithDate(
          userId: 'demo-user',
          originalText: template,
          summary: template.length > 50 ? '${template.substring(0, 47)}...' : template,
          moodTag: mood,
          triggerPhrase: 'voice input',
          tags: [mood, 'demo'],
          createdAt: now.subtract(Duration(days: i + 1)),
        ),
      );
    }

    return memories;
  }

  /// Check if dummy data should be used
  static bool shouldUseDummyData() {
    // In a real app, this could check for a debug flag or user preference
    return true; // For demo purposes, always use dummy data
  }
}
