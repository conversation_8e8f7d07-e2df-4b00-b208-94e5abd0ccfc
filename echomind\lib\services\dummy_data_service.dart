import '../models/memory_entry.dart';

/// Service for generating dummy data to simulate memory history
class DummyDataService {
  /// Generate dummy memories for demonstration
  static List<MemoryEntry> generateDummyMemories() {
    final now = DateTime.now();
    
    return [
      // Recent memories (last 7 days)
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: '<PERSON><PERSON>, f<PERSON><PERSON> to call mom about dinner plans for Sunday and ask about her doctor appointment.',
        summary: 'Call mom about Sunday dinner and doctor appointment',
        moodTag: 'concerned',
        triggerPhrase: '<PERSON><PERSON>, fkerni',
        tags: ['family', 'dinner', 'health'],
        createdAt: now.subtract(const Duration(hours: 2)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: '<PERSON> <PERSON><PERSON>, I just had the most amazing tea at this new café downtown. The jasmine green tea was absolutely perfect - floral, delicate, and so refreshing.',
        summary: 'Amazing jasmine green tea at new downtown café',
        moodTag: 'happy',
        triggerPhrase: '<PERSON> <PERSON><PERSON>',
        tags: ['tea', 'café', 'downtown'],
        createdAt: now.subtract(const Duration(days: 1)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Note to self: submit the project proposal before 5 PM tomorrow and review the presentation slides with the team.',
        summary: 'Submit project proposal before 5 PM; review slides with team',
        moodTag: 'stressed',
        triggerPhrase: 'Note to self',
        tags: ['work', 'deadline', 'presentation'],
        createdAt: now.subtract(const Duration(days: 2)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Younes, remind me that I want to start learning guitar. I saw this beautiful acoustic guitar at the music store and I think it would be a great hobby.',
        summary: 'Start learning guitar - saw beautiful acoustic at music store',
        moodTag: 'motivated',
        triggerPhrase: 'Younes, remind me',
        tags: ['guitar', 'hobby', 'music'],
        createdAt: now.subtract(const Duration(days: 3)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'I had such a peaceful morning walk in the park today. The birds were singing, the sun was gentle, and I felt so grateful for this moment of calm.',
        summary: 'Peaceful morning walk in park - grateful for calm moment',
        moodTag: 'peaceful',
        triggerPhrase: 'voice input',
        tags: ['walk', 'park', 'morning', 'gratitude'],
        createdAt: now.subtract(const Duration(days: 4)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Hey Younes, I need to buy groceries tomorrow - milk, bread, eggs, and some fresh vegetables for the week.',
        summary: 'Buy groceries: milk, bread, eggs, fresh vegetables',
        moodTag: 'neutral',
        triggerPhrase: 'Hey Younes',
        tags: ['groceries', 'shopping', 'food'],
        createdAt: now.subtract(const Duration(days: 5)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Meeting with Sarah at 3 PM to discuss the project timeline and budget requirements for the new client.',
        summary: 'Meeting with Sarah at 3 PM: project timeline and budget',
        moodTag: 'neutral',
        triggerPhrase: 'voice input',
        tags: ['meeting', 'sarah', 'project', 'budget'],
        createdAt: now.subtract(const Duration(days: 6)),
      ),
      
      // Older memories (last month)
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Younes, fkerni that I discovered this amazing book recommendation - "The Seven Husbands of Evelyn Hugo". Everyone says it\'s incredible.',
        summary: 'Book recommendation: "The Seven Husbands of Evelyn Hugo"',
        moodTag: 'excited',
        triggerPhrase: 'Younes, fkerni',
        tags: ['book', 'recommendation', 'reading'],
        createdAt: now.subtract(const Duration(days: 10)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'I\'m feeling really overwhelmed with work lately. Too many deadlines and not enough time to breathe.',
        summary: 'Feeling overwhelmed with work - too many deadlines',
        moodTag: 'stressed',
        triggerPhrase: 'voice input',
        tags: ['work', 'stress', 'deadlines'],
        createdAt: now.subtract(const Duration(days: 12)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Note to self: research vacation destinations for summer. Maybe somewhere with mountains and good hiking trails.',
        summary: 'Research summer vacation destinations with mountains and hiking',
        moodTag: 'motivated',
        triggerPhrase: 'Note to self',
        tags: ['vacation', 'mountains', 'hiking', 'summer'],
        createdAt: now.subtract(const Duration(days: 15)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Had dinner with old friends tonight. It was so wonderful to catch up and laugh together. I feel so grateful for these friendships.',
        summary: 'Wonderful dinner with old friends - grateful for friendships',
        moodTag: 'grateful',
        triggerPhrase: 'voice input',
        tags: ['friends', 'dinner', 'gratitude'],
        createdAt: now.subtract(const Duration(days: 18)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Younes, remind me to water the plants, feed the cat, and lock the door before leaving for the weekend trip.',
        summary: 'Before weekend trip: water plants, feed cat, lock door',
        moodTag: 'neutral',
        triggerPhrase: 'Younes, remind me',
        tags: ['plants', 'cat', 'weekend', 'trip'],
        createdAt: now.subtract(const Duration(days: 20)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'I\'ve been thinking a lot about my career lately. Maybe it\'s time for a change, something more creative and fulfilling.',
        summary: 'Reflecting on career - considering creative, fulfilling change',
        moodTag: 'reflective',
        triggerPhrase: 'voice input',
        tags: ['career', 'change', 'creative'],
        createdAt: now.subtract(const Duration(days: 25)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Hey Younes, I want to remember this moment. Just finished my first 5K run and I feel absolutely amazing!',
        summary: 'Finished first 5K run - feeling amazing!',
        moodTag: 'excited',
        triggerPhrase: 'Hey Younes',
        tags: ['running', '5k', 'achievement', 'fitness'],
        createdAt: now.subtract(const Duration(days: 28)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Note to self: mom\'s birthday is next month. Need to plan something special and meaningful for her.',
        summary: 'Plan special birthday celebration for mom next month',
        moodTag: 'thoughtful',
        triggerPhrase: 'Note to self',
        tags: ['mom', 'birthday', 'celebration'],
        createdAt: now.subtract(const Duration(days: 30)),
      ),
      
      // Older memories (2-3 months ago)
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Discovered this incredible tea blend today - Earl Grey with lavender. It\'s like drinking a garden in spring.',
        summary: 'Incredible Earl Grey with lavender tea blend',
        moodTag: 'happy',
        triggerPhrase: 'voice input',
        tags: ['tea', 'earl grey', 'lavender'],
        createdAt: now.subtract(const Duration(days: 45)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Younes, fkerni that I want to learn French this year. I found a great language exchange app.',
        summary: 'Goal: learn French this year using language exchange app',
        moodTag: 'motivated',
        triggerPhrase: 'Younes, fkerni',
        tags: ['french', 'language', 'learning', 'goal'],
        createdAt: now.subtract(const Duration(days: 60)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Feeling sad today. Sometimes life feels overwhelming and I miss the simplicity of childhood.',
        summary: 'Feeling sad and overwhelmed - missing childhood simplicity',
        moodTag: 'sad',
        triggerPhrase: 'voice input',
        tags: ['sadness', 'childhood', 'overwhelm'],
        createdAt: now.subtract(const Duration(days: 75)),
      ),
      
      MemoryEntry.create(
        userId: 'demo-user',
        originalText: 'Hey Younes, I just had the most productive day! Finished three major tasks and still have energy for more.',
        summary: 'Most productive day - finished three major tasks with energy',
        moodTag: 'motivated',
        triggerPhrase: 'Hey Younes',
        tags: ['productivity', 'tasks', 'energy'],
        createdAt: now.subtract(const Duration(days: 90)),
      ),
    ];
  }

  /// Generate memories for a specific mood (for testing)
  static List<MemoryEntry> generateMemoriesForMood(String mood, int count) {
    final now = DateTime.now();
    final memories = <MemoryEntry>[];
    
    final moodTemplates = {
      'happy': [
        'I feel so joyful today! Everything seems bright and wonderful.',
        'Had an amazing time with friends. Laughter really is the best medicine.',
        'Accomplished my goal today and I\'m celebrating this victory!',
      ],
      'stressed': [
        'Feeling overwhelmed with all these deadlines approaching.',
        'Too much pressure at work and not enough time to handle everything.',
        'Anxious about the presentation tomorrow. Need to prepare more.',
      ],
      'peaceful': [
        'Such a calm and serene morning. I feel completely at peace.',
        'Meditation session was perfect today. Mind feels clear and centered.',
        'Quiet evening with a good book and tea. Pure contentment.',
      ],
      'reflective': [
        'Been thinking deeply about life choices and future directions.',
        'Contemplating the meaning of recent events and their impact.',
        'Reflecting on past experiences and lessons learned.',
      ],
    };
    
    final templates = moodTemplates[mood] ?? moodTemplates['neutral']!;
    
    for (int i = 0; i < count; i++) {
      final template = templates[i % templates.length];
      memories.add(
        MemoryEntry.create(
          userId: 'demo-user',
          originalText: template,
          summary: template.length > 50 ? '${template.substring(0, 47)}...' : template,
          moodTag: mood,
          triggerPhrase: 'voice input',
          tags: [mood, 'demo'],
          createdAt: now.subtract(Duration(days: i + 1)),
        ),
      );
    }
    
    return memories;
  }

  /// Check if dummy data should be used
  static bool shouldUseDummyData() {
    // In a real app, this could check for a debug flag or user preference
    return true; // For demo purposes, always use dummy data
  }
}
