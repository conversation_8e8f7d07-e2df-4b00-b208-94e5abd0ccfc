import 'dart:convert';
import 'package:http/http.dart' as http;
import '../core/app_constants.dart';
import '../models/memory_entry.dart';

class GeminiService {
  static final GeminiService _instance = GeminiService._internal();
  factory GeminiService() => _instance;
  GeminiService._internal();

  static const String _baseUrl =
      'https://generativelanguage.googleapis.com/v1beta';

  /// Summarize voice journal entry into a concise, helpful memory
  Future<String> summarizeMemory(String inputText) async {
    try {
      final prompt =
          '''
Summarize the following voice journal as a helpful memory for future reference.

Entry: "$inputText"

Instructions:
- Create a concise, actionable summary
- Keep tone friendly and neutral
- Focus on key actions, people, and important details
- Maximum 100 characters
- Return only the summary, nothing else

Example:
Entry: "You<PERSON>, remind me to check my email for the class schedule and message Salma."
Summary: "Check email for class schedule; message Salma."
''';

      final response = await _generateContent(prompt);
      final summary = response.trim();

      // Ensure summary is not too long
      if (summary.length > 100) {
        return '${summary.substring(0, 97)}...';
      }

      return summary.isNotEmpty ? summary : _generateFallbackSummary(inputText);
    } catch (e) {
      // GeminiService: Error summarizing memory: $e
      return _generateFallbackSummary(inputText);
    }
  }

  /// Generate a title for memory content
  Future<String> generateTitle(String content) async {
    try {
      final prompt =
          '''
      Generate a concise, descriptive title (max 50 characters) for this memory content:
      
      "$content"
      
      Return only the title, nothing else.
      ''';

      final response = await _generateContent(prompt);
      return response.trim();
    } catch (e) {
      // GeminiService: Error generating title: $e
      return _generateFallbackTitle(content);
    }
  }

  /// Generate tags for memory content
  Future<List<String>> generateTags(String content) async {
    try {
      final prompt =
          '''
      Generate 3-5 relevant tags for this memory content. Return only the tags separated by commas:
      
      "$content"
      
      Tags should be single words or short phrases, relevant to the content.
      ''';

      final response = await _generateContent(prompt);
      return response
          .split(',')
          .map((tag) => tag.trim())
          .where((tag) => tag.isNotEmpty)
          .take(5)
          .toList();
    } catch (e) {
      // GeminiService: Error generating tags: $e
      return _generateFallbackTags(content);
    }
  }

  /// Summarize memory content
  Future<String> summarizeContent(String content) async {
    try {
      final prompt =
          '''
      Provide a concise summary (max 100 words) of this memory content:
      
      "$content"
      
      Focus on the key points and main ideas.
      ''';

      final response = await _generateContent(prompt);
      return response.trim();
    } catch (e) {
      // GeminiService: Error summarizing content: $e
      return content.length > 100 ? '${content.substring(0, 97)}...' : content;
    }
  }

  /// Analyze sentiment and determine mood
  Future<String> analyzeMood(String content) async {
    try {
      final prompt =
          '''
Analyze the emotional tone of this memory and return ONE mood category.

Content: "$content"

Choose from these moods:
- happy: positive, joyful, excited, grateful
- stressed: anxious, worried, overwhelmed, urgent
- reflective: thoughtful, contemplative, nostalgic
- neutral: factual, routine, informational
- sad: disappointed, melancholy, upset
- motivated: determined, ambitious, goal-oriented
- peaceful: calm, relaxed, content

Return only the mood word, nothing else.
''';

      final response = await _generateContent(prompt);

      if (response.isNotEmpty) {
        final mood = response.trim().toLowerCase();
        const validMoods = [
          'happy',
          'stressed',
          'reflective',
          'neutral',
          'sad',
          'motivated',
          'peaceful',
        ];

        if (validMoods.contains(mood)) {
          return mood;
        }
      }

      return _analyzeMoodFallback(content);
    } catch (e) {
      return _analyzeMoodFallback(content);
    }
  }

  /// Suggest category for memory content
  Future<String> suggestCategory(String content) async {
    try {
      final categories = AppConstants.memoryCategories.join(', ');
      final prompt =
          '''
      Suggest the most appropriate category for this memory content from these options:
      $categories

      Content: "$content"

      Return only the category name, nothing else.
      ''';

      final response = await _generateContent(prompt);
      final suggestedCategory = response.trim();

      // Validate that the suggested category is in our list
      if (AppConstants.memoryCategories.contains(suggestedCategory)) {
        return suggestedCategory;
      } else {
        return 'Other';
      }
    } catch (e) {
      // GeminiService: Error suggesting category: $e
      return 'Other';
    }
  }

  /// Enhance memory content with AI insights
  Future<MemoryInsights> generateInsights(MemoryEntry memory) async {
    try {
      final prompt =
          '''
      Analyze this memory and provide insights:
      
      Title: ${memory.title}
      Content: ${memory.content}
      Category: ${memory.category}
      
      Provide:
      1. Key themes (max 3)
      2. Emotional tone (positive/neutral/negative)
      3. Action items (if any)
      4. Related concepts
      
      Format as JSON with keys: themes, tone, actionItems, relatedConcepts
      ''';

      final response = await _generateContent(prompt);
      return MemoryInsights.fromJson(json.decode(response));
    } catch (e) {
      // GeminiService: Error generating insights: $e
      return MemoryInsights.fallback();
    }
  }

  /// Search memories using semantic similarity
  Future<List<MemoryEntry>> semanticSearch(
    String query,
    List<MemoryEntry> memories,
  ) async {
    try {
      // TODO: Implement actual semantic search using embeddings
      // For now, use simple text matching
      final lowercaseQuery = query.toLowerCase();

      return memories.where((memory) {
        return memory.title.toLowerCase().contains(lowercaseQuery) ||
            memory.content.toLowerCase().contains(lowercaseQuery) ||
            memory.tags.any(
              (tag) => tag.toLowerCase().contains(lowercaseQuery),
            );
      }).toList();
    } catch (e) {
      // GeminiService: Error in semantic search: $e
      return [];
    }
  }

  /// Generate content using Gemini API
  Future<String> _generateContent(String prompt) async {
    try {
      final url =
          '$_baseUrl/models/gemini-pro:generateContent?key=${AppConstants.geminiApiKey}';

      final response = await http.post(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'contents': [
            {
              'parts': [
                {'text': prompt},
              ],
            },
          ],
          'generationConfig': {
            'temperature': 0.7,
            'topK': 40,
            'topP': 0.95,
            'maxOutputTokens': 1024,
          },
        }),
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        final candidates = jsonResponse['candidates'] as List;
        if (candidates.isNotEmpty) {
          final content =
              candidates[0]['content']['parts'][0]['text'] as String;
          return content;
        }
      }

      throw Exception('Gemini API error: ${response.statusCode}');
    } catch (e) {
      // GeminiService: API call failed: $e
      rethrow;
    }
  }

  /// Generate fallback summary when AI fails
  String _generateFallbackSummary(String content) {
    // Remove trigger phrases and clean up text
    String cleanContent = content
        .toLowerCase()
        .replaceAll(
          RegExp(
            r'(younes|hey younes|fkerni|note to self|remind me to|remind me)',
            caseSensitive: false,
          ),
          '',
        )
        .trim();

    // Take first meaningful words and create summary
    final words = cleanContent
        .split(' ')
        .where((word) => word.isNotEmpty && word.length > 2)
        .take(8)
        .join(' ');

    if (words.isEmpty) {
      return 'Voice memory captured';
    }

    // Ensure it's not too long
    return words.length > 100 ? '${words.substring(0, 97)}...' : words;
  }

  /// Generate fallback title when AI fails
  String _generateFallbackTitle(String content) {
    final words = content.split(' ').take(5).join(' ');
    return words.length > 50 ? '${words.substring(0, 47)}...' : words;
  }

  /// Generate fallback tags when AI fails
  /// Analyze mood using keyword matching fallback
  String _analyzeMoodFallback(String content) {
    final lowercaseContent = content.toLowerCase();

    // Happy keywords
    if (RegExp(
      r'\b(happy|joy|excited|great|awesome|love|amazing|wonderful|fantastic|good|success|achieve|celebrate)\b',
    ).hasMatch(lowercaseContent)) {
      return 'happy';
    }

    // Stressed keywords
    if (RegExp(
      r'\b(stress|urgent|deadline|worry|anxious|overwhelm|pressure|rush|panic|crisis|emergency)\b',
    ).hasMatch(lowercaseContent)) {
      return 'stressed';
    }

    // Sad keywords
    if (RegExp(
      r'\b(sad|disappoint|upset|hurt|pain|loss|miss|regret|sorry|difficult|hard)\b',
    ).hasMatch(lowercaseContent)) {
      return 'sad';
    }

    // Motivated keywords
    if (RegExp(
      r'\b(goal|plan|achieve|focus|determine|ambition|target|objective|improve|progress)\b',
    ).hasMatch(lowercaseContent)) {
      return 'motivated';
    }

    // Peaceful keywords
    if (RegExp(
      r'\b(calm|peace|relax|quiet|serene|content|comfortable|rest|meditate|breathe)\b',
    ).hasMatch(lowercaseContent)) {
      return 'peaceful';
    }

    // Reflective keywords
    if (RegExp(
      r'\b(think|reflect|consider|ponder|remember|memory|past|future|wonder|contemplate)\b',
    ).hasMatch(lowercaseContent)) {
      return 'reflective';
    }

    // Default to neutral
    return 'neutral';
  }

  List<String> _generateFallbackTags(String content) {
    // Simple keyword extraction
    final words = content
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), '')
        .split(' ')
        .where((word) => word.length > 3)
        .toSet()
        .take(3)
        .toList();
    return words;
  }

  /// Check if Gemini service is available
  Future<bool> isServiceAvailable() async {
    try {
      return AppConstants.geminiApiKey.isNotEmpty &&
          AppConstants.geminiApiKey != 'YOUR_GEMINI_API_KEY';
    } catch (e) {
      return false;
    }
  }
}

class MemoryInsights {
  final List<String> themes;
  final String tone;
  final List<String> actionItems;
  final List<String> relatedConcepts;

  const MemoryInsights({
    required this.themes,
    required this.tone,
    required this.actionItems,
    required this.relatedConcepts,
  });

  factory MemoryInsights.fromJson(Map<String, dynamic> json) {
    return MemoryInsights(
      themes: (json['themes'] as List<dynamic>?)?.cast<String>() ?? [],
      tone: json['tone'] as String? ?? 'neutral',
      actionItems:
          (json['actionItems'] as List<dynamic>?)?.cast<String>() ?? [],
      relatedConcepts:
          (json['relatedConcepts'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  factory MemoryInsights.fallback() {
    return const MemoryInsights(
      themes: ['General'],
      tone: 'neutral',
      actionItems: [],
      relatedConcepts: [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'themes': themes,
      'tone': tone,
      'actionItems': actionItems,
      'relatedConcepts': relatedConcepts,
    };
  }
}
