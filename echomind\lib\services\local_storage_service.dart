import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/memory_entry.dart';

/// Service for local-only memory storage
class LocalStorageService {
  static const String _memoriesKey = 'local_memories';
  static const String _userIdKey = 'local_user_id';
  static const String _storagePreferenceKey = 'storage_preference';

  /// Save memory to local storage
  Future<void> saveMemory(MemoryEntry memory) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final memories = await getMemories();
      
      // Add new memory
      memories.add(memory);
      
      // Convert to JSON and save
      final memoriesJson = memories.map((m) => m.toJson()).toList();
      await prefs.setString(_memoriesKey, json.encode(memoriesJson));
      
      // LocalStorageService: Memory saved locally
    } catch (e) {
      // LocalStorageService: Error saving memory: $e
      rethrow;
    }
  }

  /// Get all memories from local storage
  Future<List<MemoryEntry>> getMemories() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final memoriesString = prefs.getString(_memoriesKey);
      
      if (memoriesString == null || memoriesString.isEmpty) {
        return [];
      }
      
      final memoriesJson = json.decode(memoriesString) as List;
      return memoriesJson
          .map((json) => MemoryEntry.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      // LocalStorageService: Error loading memories: $e
      return [];
    }
  }

  /// Update existing memory
  Future<void> updateMemory(MemoryEntry updatedMemory) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final memories = await getMemories();
      
      // Find and update memory
      final index = memories.indexWhere((m) => m.id == updatedMemory.id);
      if (index != -1) {
        memories[index] = updatedMemory;
        
        // Save updated list
        final memoriesJson = memories.map((m) => m.toJson()).toList();
        await prefs.setString(_memoriesKey, json.encode(memoriesJson));
      }
    } catch (e) {
      // LocalStorageService: Error updating memory: $e
      rethrow;
    }
  }

  /// Delete memory from local storage
  Future<void> deleteMemory(String memoryId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final memories = await getMemories();
      
      // Remove memory
      memories.removeWhere((m) => m.id == memoryId);
      
      // Save updated list
      final memoriesJson = memories.map((m) => m.toJson()).toList();
      await prefs.setString(_memoriesKey, json.encode(memoriesJson));
    } catch (e) {
      // LocalStorageService: Error deleting memory: $e
      rethrow;
    }
  }

  /// Search memories locally
  Future<List<MemoryEntry>> searchMemories(String query) async {
    try {
      final memories = await getMemories();
      final lowercaseQuery = query.toLowerCase();

      return memories.where((memory) {
        return memory.summary?.toLowerCase().contains(lowercaseQuery) == true ||
            memory.originalText.toLowerCase().contains(lowercaseQuery) ||
            memory.moodTag?.toLowerCase().contains(lowercaseQuery) == true ||
            memory.triggerPhrase?.toLowerCase().contains(lowercaseQuery) == true ||
            memory.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery));
      }).toList();
    } catch (e) {
      // LocalStorageService: Error searching memories: $e
      return [];
    }
  }

  /// Get memories by mood
  Future<List<MemoryEntry>> getMemoriesByMood(String mood) async {
    try {
      final memories = await getMemories();
      return memories.where((memory) => memory.moodTag == mood).toList();
    } catch (e) {
      // LocalStorageService: Error filtering by mood: $e
      return [];
    }
  }

  /// Get memories by date range
  Future<List<MemoryEntry>> getMemoriesByDateRange(DateTime start, DateTime end) async {
    try {
      final memories = await getMemories();
      return memories.where((memory) {
        return memory.createdAt.isAfter(start) && memory.createdAt.isBefore(end);
      }).toList();
    } catch (e) {
      // LocalStorageService: Error filtering by date: $e
      return [];
    }
  }

  /// Get memory statistics
  Future<MemoryStats> getMemoryStats() async {
    try {
      final memories = await getMemories();
      
      // Count by mood
      final moodCounts = <String, int>{};
      for (final memory in memories) {
        final mood = memory.moodTag ?? 'neutral';
        moodCounts[mood] = (moodCounts[mood] ?? 0) + 1;
      }
      
      // Calculate date range
      DateTime? earliest;
      DateTime? latest;
      
      if (memories.isNotEmpty) {
        earliest = memories.map((m) => m.createdAt).reduce((a, b) => a.isBefore(b) ? a : b);
        latest = memories.map((m) => m.createdAt).reduce((a, b) => a.isAfter(b) ? a : b);
      }
      
      return MemoryStats(
        totalMemories: memories.length,
        moodCounts: moodCounts,
        earliestMemory: earliest,
        latestMemory: latest,
        averageMemoryLength: memories.isEmpty 
            ? 0 
            : memories.map((m) => m.originalText.length).reduce((a, b) => a + b) / memories.length,
      );
    } catch (e) {
      // LocalStorageService: Error calculating stats: $e
      return MemoryStats.empty();
    }
  }

  /// Set storage preference (local vs cloud)
  Future<void> setStoragePreference(StoragePreference preference) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_storagePreferenceKey, preference.toString());
  }

  /// Get storage preference
  Future<StoragePreference> getStoragePreference() async {
    final prefs = await SharedPreferences.getInstance();
    final preferenceString = prefs.getString(_storagePreferenceKey);
    
    if (preferenceString == null) {
      return StoragePreference.cloud; // Default to cloud
    }
    
    return StoragePreference.values.firstWhere(
      (e) => e.toString() == preferenceString,
      orElse: () => StoragePreference.cloud,
    );
  }

  /// Set local user ID
  Future<void> setLocalUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, userId);
  }

  /// Get local user ID
  Future<String?> getLocalUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  /// Clear all local data
  Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_memoriesKey);
    await prefs.remove(_userIdKey);
  }

  /// Export memories to JSON
  Future<String> exportMemories() async {
    try {
      final memories = await getMemories();
      final export = {
        'version': '1.0',
        'exportDate': DateTime.now().toIso8601String(),
        'totalMemories': memories.length,
        'memories': memories.map((m) => m.toJson()).toList(),
      };
      
      return json.encode(export);
    } catch (e) {
      // LocalStorageService: Error exporting memories: $e
      rethrow;
    }
  }

  /// Import memories from JSON
  Future<int> importMemories(String jsonData) async {
    try {
      final importData = json.decode(jsonData) as Map<String, dynamic>;
      final memoriesJson = importData['memories'] as List;
      
      final importedMemories = memoriesJson
          .map((json) => MemoryEntry.fromJson(json as Map<String, dynamic>))
          .toList();
      
      // Get existing memories
      final existingMemories = await getMemories();
      final existingIds = existingMemories.map((m) => m.id).toSet();
      
      // Add only new memories (avoid duplicates)
      final newMemories = importedMemories
          .where((m) => !existingIds.contains(m.id))
          .toList();
      
      // Save all memories
      final allMemories = [...existingMemories, ...newMemories];
      final prefs = await SharedPreferences.getInstance();
      final memoriesJson = allMemories.map((m) => m.toJson()).toList();
      await prefs.setString(_memoriesKey, json.encode(memoriesJson));
      
      return newMemories.length;
    } catch (e) {
      // LocalStorageService: Error importing memories: $e
      rethrow;
    }
  }

  /// Check if local storage is available
  Future<bool> isAvailable() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs != null;
    } catch (e) {
      return false;
    }
  }

  /// Get storage usage info
  Future<StorageInfo> getStorageInfo() async {
    try {
      final memories = await getMemories();
      final memoriesJson = memories.map((m) => m.toJson()).toList();
      final jsonString = json.encode(memoriesJson);
      final sizeInBytes = utf8.encode(jsonString).length;
      
      return StorageInfo(
        totalMemories: memories.length,
        sizeInBytes: sizeInBytes,
        sizeInKB: sizeInBytes / 1024,
        sizeInMB: sizeInBytes / (1024 * 1024),
      );
    } catch (e) {
      return StorageInfo.empty();
    }
  }
}

/// Storage preference enum
enum StoragePreference {
  local,
  cloud,
  hybrid, // Use both local and cloud
}

/// Memory statistics
class MemoryStats {
  final int totalMemories;
  final Map<String, int> moodCounts;
  final DateTime? earliestMemory;
  final DateTime? latestMemory;
  final double averageMemoryLength;

  const MemoryStats({
    required this.totalMemories,
    required this.moodCounts,
    required this.earliestMemory,
    required this.latestMemory,
    required this.averageMemoryLength,
  });

  factory MemoryStats.empty() {
    return const MemoryStats(
      totalMemories: 0,
      moodCounts: {},
      earliestMemory: null,
      latestMemory: null,
      averageMemoryLength: 0,
    );
  }
}

/// Storage information
class StorageInfo {
  final int totalMemories;
  final int sizeInBytes;
  final double sizeInKB;
  final double sizeInMB;

  const StorageInfo({
    required this.totalMemories,
    required this.sizeInBytes,
    required this.sizeInKB,
    required this.sizeInMB,
  });

  factory StorageInfo.empty() {
    return const StorageInfo(
      totalMemories: 0,
      sizeInBytes: 0,
      sizeInKB: 0,
      sizeInMB: 0,
    );
  }
}
