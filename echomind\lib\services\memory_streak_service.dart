import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/memory_entry.dart';

/// Service for tracking memory streaks and XP
class MemoryStreakService {
  static const String _streakKey = 'memory_streak';
  static const String _xpKey = 'memory_xp';
  static const String _lastMemoryDateKey = 'last_memory_date';
  static const String _totalMemoriesKey = 'total_memories';
  static const String _longestStreakKey = 'longest_streak';

  /// Get current memory streak
  Future<int> getCurrentStreak() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_streakKey) ?? 0;
  }

  /// Get total XP points
  Future<int> getTotalXP() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_xpKey) ?? 0;
  }

  /// Get total memories count
  Future<int> getTotalMemories() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_totalMemoriesKey) ?? 0;
  }

  /// Get longest streak achieved
  Future<int> getLongestStreak() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_longestStreakKey) ?? 0;
  }

  /// Update streak when a new memory is added
  Future<StreakUpdate> updateStreakForNewMemory(MemoryEntry memory) async {
    final prefs = await SharedPreferences.getInstance();

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Get last memory date
    final lastMemoryDateString = prefs.getString(_lastMemoryDateKey);
    DateTime? lastMemoryDate;

    if (lastMemoryDateString != null) {
      lastMemoryDate = DateTime.parse(lastMemoryDateString);
      lastMemoryDate = DateTime(
        lastMemoryDate.year,
        lastMemoryDate.month,
        lastMemoryDate.day,
      );
    }

    // Get current values
    int currentStreak = await getCurrentStreak();
    int totalXP = await getTotalXP();
    int totalMemories = await getTotalMemories();
    int longestStreak = await getLongestStreak();

    // Calculate new streak
    int newStreak = currentStreak;
    bool isNewDay = false;
    bool streakBroken = false;

    if (lastMemoryDate == null) {
      // First memory ever
      newStreak = 1;
      isNewDay = true;
    } else if (lastMemoryDate.isAtSameMomentAs(today)) {
      // Same day, no streak change
      newStreak = currentStreak;
    } else if (lastMemoryDate
        .add(const Duration(days: 1))
        .isAtSameMomentAs(today)) {
      // Consecutive day
      newStreak = currentStreak + 1;
      isNewDay = true;
    } else {
      // Streak broken
      newStreak = 1;
      isNewDay = true;
      streakBroken = true;
    }

    // Calculate XP gain
    int xpGain = _calculateXPGain(memory, newStreak, isNewDay);
    int newTotalXP = totalXP + xpGain;
    int newTotalMemories = totalMemories + 1;

    // Update longest streak
    int newLongestStreak = longestStreak;
    if (newStreak > longestStreak) {
      newLongestStreak = newStreak;
    }

    // Save updated values
    await prefs.setInt(_streakKey, newStreak);
    await prefs.setInt(_xpKey, newTotalXP);
    await prefs.setInt(_totalMemoriesKey, newTotalMemories);
    await prefs.setInt(_longestStreakKey, newLongestStreak);
    await prefs.setString(_lastMemoryDateKey, today.toIso8601String());

    return StreakUpdate(
      previousStreak: currentStreak,
      newStreak: newStreak,
      xpGained: xpGain,
      totalXP: newTotalXP,
      totalMemories: newTotalMemories,
      longestStreak: newLongestStreak,
      isNewDay: isNewDay,
      streakBroken: streakBroken,
      achievements: _checkAchievements(
        newStreak,
        newTotalMemories,
        newLongestStreak,
      ),
    );
  }

  /// Calculate XP gain for a memory
  int _calculateXPGain(MemoryEntry memory, int currentStreak, bool isNewDay) {
    int baseXP = 10; // Base XP for any memory

    // Bonus for new day
    if (isNewDay) {
      baseXP += 5;
    }

    // Streak multiplier
    if (currentStreak >= 7) {
      baseXP += 10; // Weekly streak bonus
    }
    if (currentStreak >= 30) {
      baseXP += 20; // Monthly streak bonus
    }

    // Mood bonus (encourage emotional awareness)
    if (memory.moodTag != null && memory.moodTag != 'neutral') {
      baseXP += 3;
    }

    // Length bonus (encourage detailed memories)
    if (memory.originalText.length > 100) {
      baseXP += 2;
    }

    // Tags bonus (encourage categorization)
    if (memory.tags.isNotEmpty) {
      baseXP += memory.tags.length;
    }

    return baseXP;
  }

  /// Check for achievements
  List<Achievement> _checkAchievements(
    int streak,
    int totalMemories,
    int longestStreak,
  ) {
    List<Achievement> achievements = [];

    // Streak achievements
    if (streak == 3) {
      achievements.add(
        Achievement.streakAchievement(
          3,
          '🔥 Getting Started!',
          'Created memories for 3 days in a row',
        ),
      );
    } else if (streak == 7) {
      achievements.add(
        Achievement.streakAchievement(
          7,
          '📅 Week Warrior!',
          'One week of daily memories',
        ),
      );
    } else if (streak == 30) {
      achievements.add(
        Achievement.streakAchievement(
          30,
          '🌟 Memory Master!',
          'One month of daily memories',
        ),
      );
    } else if (streak == 100) {
      achievements.add(
        Achievement.streakAchievement(
          100,
          '💎 Centurion!',
          '100 days of memories',
        ),
      );
    }

    // Total memories achievements
    if (totalMemories == 10) {
      achievements.add(
        Achievement.countAchievement(
          10,
          '📝 Storyteller',
          'Created your first 10 memories',
        ),
      );
    } else if (totalMemories == 50) {
      achievements.add(
        Achievement.countAchievement(
          50,
          '📚 Chronicler',
          '50 memories captured',
        ),
      );
    } else if (totalMemories == 100) {
      achievements.add(
        Achievement.countAchievement(
          100,
          '🏆 Memory Keeper',
          '100 memories and counting',
        ),
      );
    } else if (totalMemories == 500) {
      achievements.add(
        Achievement.countAchievement(
          500,
          '👑 Legend',
          '500 memories preserved',
        ),
      );
    }

    return achievements;
  }

  /// Get streak status for display
  Future<StreakStatus> getStreakStatus() async {
    final streak = await getCurrentStreak();
    final xp = await getTotalXP();
    final totalMemories = await getTotalMemories();
    final longestStreak = await getLongestStreak();

    return StreakStatus(
      currentStreak: streak,
      totalXP: xp,
      totalMemories: totalMemories,
      longestStreak: longestStreak,
      level: _calculateLevel(xp),
      xpToNextLevel: _calculateXPToNextLevel(xp),
      streakMessage: _getStreakMessage(streak),
    );
  }

  /// Calculate user level based on XP
  int _calculateLevel(int xp) {
    // Level formula: level = sqrt(xp / 100)
    return sqrt(xp / 100).floor() + 1;
  }

  /// Calculate XP needed for next level
  int _calculateXPToNextLevel(int currentXP) {
    final currentLevel = _calculateLevel(currentXP);
    final nextLevelXP = (currentLevel * currentLevel) * 100;
    return nextLevelXP - currentXP;
  }

  /// Get encouraging message based on streak
  String _getStreakMessage(int streak) {
    if (streak == 0) {
      return "Start your memory journey today! 🌱";
    } else if (streak == 1) {
      return "Great start! Keep it going tomorrow 💪";
    } else if (streak < 7) {
      return "Building momentum! $streak days strong 🔥";
    } else if (streak < 30) {
      return "Amazing streak! $streak days of memories 🌟";
    } else if (streak < 100) {
      return "Incredible dedication! $streak days 💎";
    } else {
      return "Legendary memory keeper! $streak days 👑";
    }
  }

  /// Reset streak (for testing or user request)
  Future<void> resetStreak() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_streakKey);
    await prefs.remove(_lastMemoryDateKey);
  }

  /// Reset all data (for testing)
  Future<void> resetAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_streakKey);
    await prefs.remove(_xpKey);
    await prefs.remove(_lastMemoryDateKey);
    await prefs.remove(_totalMemoriesKey);
    await prefs.remove(_longestStreakKey);
  }
}

/// Result of streak update
class StreakUpdate {
  final int previousStreak;
  final int newStreak;
  final int xpGained;
  final int totalXP;
  final int totalMemories;
  final int longestStreak;
  final bool isNewDay;
  final bool streakBroken;
  final List<Achievement> achievements;

  const StreakUpdate({
    required this.previousStreak,
    required this.newStreak,
    required this.xpGained,
    required this.totalXP,
    required this.totalMemories,
    required this.longestStreak,
    required this.isNewDay,
    required this.streakBroken,
    required this.achievements,
  });

  bool get streakIncreased => newStreak > previousStreak;
  bool get hasAchievements => achievements.isNotEmpty;
}

/// Current streak status
class StreakStatus {
  final int currentStreak;
  final int totalXP;
  final int totalMemories;
  final int longestStreak;
  final int level;
  final int xpToNextLevel;
  final String streakMessage;

  const StreakStatus({
    required this.currentStreak,
    required this.totalXP,
    required this.totalMemories,
    required this.longestStreak,
    required this.level,
    required this.xpToNextLevel,
    required this.streakMessage,
  });
}

/// Achievement model
class Achievement {
  final String id;
  final String title;
  final String description;
  final String emoji;
  final AchievementType type;
  final int value;

  const Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.emoji,
    required this.type,
    required this.value,
  });

  factory Achievement.streakAchievement(
    int days,
    String title,
    String description,
  ) {
    return Achievement(
      id: 'streak_$days',
      title: title,
      description: description,
      emoji: '🔥',
      type: AchievementType.streak,
      value: days,
    );
  }

  factory Achievement.countAchievement(
    int count,
    String title,
    String description,
  ) {
    return Achievement(
      id: 'count_$count',
      title: title,
      description: description,
      emoji: '📝',
      type: AchievementType.count,
      value: count,
    );
  }
}

enum AchievementType { streak, count, special }
