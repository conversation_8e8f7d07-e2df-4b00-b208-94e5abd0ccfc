import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import '../core/app_constants.dart';
import '../models/memory_entry.dart';

class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  supabase.SupabaseClient get _client => supabase.Supabase.instance.client;
  bool _isInitialized = false;

  /// Initialize Supabase client
  Future<void> initialize() async {
    try {
      await supabase.Supabase.initialize(
        url: AppConstants.supabaseUrl,
        anonKey: AppConstants.supabaseAnonKey,
      );

      _isInitialized = true;
      print('SupabaseService: Initialized successfully');
    } catch (e) {
      print('SupabaseService: Initialization failed: $e');
      rethrow;
    }
  }

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  // AUTHENTICATION METHODS

  /// Sign up with email and password
  Future<supabase.AuthResponse> signUp(String email, String password) async {
    try {
      print('SupabaseService: Signing up user with email: $email');

      final response = await _client.auth.signUp(
        email: email,
        password: password,
      );

      return response;
    } catch (e) {
      print('SupabaseService: Sign up failed: $e');
      rethrow;
    }
  }

  /// Sign in with email and password
  Future<supabase.AuthResponse> signIn(String email, String password) async {
    try {
      print('SupabaseService: Signing in user with email: $email');

      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      return response;
    } catch (e) {
      print('SupabaseService: Sign in failed: $e');
      rethrow;
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    try {
      await _client.auth.signOut();
      print('SupabaseService: User signed out successfully');
    } catch (e) {
      print('SupabaseService: Sign out failed: $e');
      rethrow;
    }
  }

  /// Get current user
  supabase.User? getCurrentUser() {
    try {
      return _client.auth.currentUser;
    } catch (e) {
      print('SupabaseService: Get current user failed: $e');
      return null;
    }
  }

  /// Get current user ID
  String? getCurrentUserId() {
    return _client.auth.currentUser?.id;
  }

  // MEMORY CRUD OPERATIONS

  /// Save memory to Supabase
  Future<MemoryEntry> saveMemory(MemoryEntry memory) async {
    try {
      print('SupabaseService: Saving memory: ${memory.title}');

      final userId = getCurrentUserId();
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Prepare data for Supabase (only the fields that exist in the table)
      final memoryData = {
        'user_id': userId,
        'summary': memory.summary,
        'original_text': memory.originalText,
        'mood_tag': memory.moodTag,
        'trigger_phrase': memory.triggerPhrase,
        'storage_mode': memory.storageMode,
      };

      final response = await _client
          .from('memories')
          .insert(memoryData)
          .select()
          .single();

      return MemoryEntry.fromJson(response);
    } catch (e) {
      print('SupabaseService: Save memory failed: $e');
      rethrow;
    }
  }

  /// Get all memories for current user
  Future<List<MemoryEntry>> getUserMemories() async {
    try {
      print('SupabaseService: Fetching memories');

      final userId = getCurrentUserId();
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final response = await _client
          .from('memories')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return (response as List)
          .map((json) => MemoryEntry.fromJson(json))
          .toList();
    } catch (e) {
      print('SupabaseService: Get memories failed: $e');
      rethrow;
    }
  }

  /// Get all memories for current user (backward compatibility)
  Future<List<MemoryEntry>> getMemories() => getUserMemories();

  /// Get memory by ID
  Future<MemoryEntry?> getMemoryById(String id) async {
    try {
      // TODO: Implement actual Supabase query
      print('SupabaseService: Fetching memory with ID: $id');

      final memories = await getMemories();
      return memories.firstWhere(
        (memory) => memory.id == id,
        orElse: () => throw Exception('Memory not found'),
      );
    } catch (e) {
      print('SupabaseService: Get memory by ID failed: $e');
      return null;
    }
  }

  /// Update memory
  Future<MemoryEntry> updateMemory(MemoryEntry memory) async {
    try {
      // TODO: Implement actual Supabase update
      print('SupabaseService: Updating memory: ${memory.title}');

      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 400));

      return memory.copyWith(updatedAt: DateTime.now());
    } catch (e) {
      print('SupabaseService: Update memory failed: $e');
      rethrow;
    }
  }

  /// Delete memory
  Future<void> deleteMemory(String id) async {
    try {
      print('SupabaseService: Deleting memory with ID: $id');

      final userId = getCurrentUserId();
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      await _client
          .from('memories')
          .delete()
          .eq('id', id)
          .eq(
            'user_id',
            userId,
          ); // Ensure user can only delete their own memories
    } catch (e) {
      print('SupabaseService: Delete memory failed: $e');
      rethrow;
    }
  }

  /// Search memories
  Future<List<MemoryEntry>> searchMemories(String query) async {
    try {
      // TODO: Implement actual Supabase full-text search
      print('SupabaseService: Searching memories with query: $query');

      final memories = await getMemories();
      final lowercaseQuery = query.toLowerCase();

      return memories.where((memory) {
        return memory.title.toLowerCase().contains(lowercaseQuery) ||
            memory.content.toLowerCase().contains(lowercaseQuery) ||
            memory.tags.any(
              (tag) => tag.toLowerCase().contains(lowercaseQuery),
            );
      }).toList();
    } catch (e) {
      print('SupabaseService: Search memories failed: $e');
      rethrow;
    }
  }

  // FILE STORAGE OPERATIONS

  /// Upload audio file
  Future<String> uploadAudioFile(String filePath) async {
    try {
      // TODO: Implement actual Supabase storage upload
      print('SupabaseService: Uploading audio file: $filePath');

      // Simulate upload delay
      await Future.delayed(const Duration(seconds: 2));

      // Return placeholder URL
      return 'https://storage.supabase.co/bucket/audio/file_${DateTime.now().millisecondsSinceEpoch}.wav';
    } catch (e) {
      print('SupabaseService: Upload audio file failed: $e');
      rethrow;
    }
  }

  /// Delete audio file
  Future<void> deleteAudioFile(String url) async {
    try {
      // TODO: Implement actual Supabase storage delete
      print('SupabaseService: Deleting audio file: $url');

      // Simulate delete delay
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      print('SupabaseService: Delete audio file failed: $e');
      rethrow;
    }
  }

  /// Generate placeholder memories for development
  List<MemoryEntry> _generatePlaceholderMemories() {
    final userId = getCurrentUserId() ?? 'placeholder-user';
    return [
      MemoryEntry.create(
        userId: userId,
        originalText:
            'Discussed project timeline and budget allocation for Q2.',
        summary: 'Meeting Notes',
        moodTag: 'focused',
        triggerPhrase: 'meeting notes',
      ),
      MemoryEntry.create(
        userId: userId,
        originalText:
            'Need to buy milk, eggs, bread, and vegetables for the week.',
        summary: 'Grocery List',
        moodTag: 'calm',
        triggerPhrase: 'grocery list',
      ),
      MemoryEntry.create(
        userId: userId,
        originalText:
            'Voice-to-text app with AI summarization and smart categorization.',
        summary: 'App Idea',
        moodTag: 'excited',
        triggerPhrase: 'app idea',
      ),
    ];
  }

  /// Check if service is available
  Future<bool> isServiceAvailable() async {
    try {
      // TODO: Implement actual health check
      return _isInitialized &&
          AppConstants.supabaseUrl.isNotEmpty &&
          AppConstants.supabaseUrl != 'YOUR_SUPABASE_URL';
    } catch (e) {
      return false;
    }
  }
}
