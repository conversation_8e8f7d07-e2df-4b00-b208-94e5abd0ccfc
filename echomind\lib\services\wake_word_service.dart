import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:speech_to_text/speech_recognition_result.dart';
import 'package:speech_to_text/speech_recognition_error.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

/// Lightweight wake word detection service for always-on listening
class WakeWordService {
  static final WakeWordService _instance = WakeWordService._internal();
  factory WakeWordService() => _instance;
  WakeWordService._internal();

  // Core components
  final SpeechToText _speechToText = SpeechToText();
  final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  // State management
  bool _isInitialized = false;
  bool _isListening = false;
  bool _isEnabled = false;
  Timer? _restartTimer;
  Timer? _batteryOptimizationTimer;

  // Stream controllers
  final StreamController<String> _wakeWordDetectedController =
      StreamController<String>.broadcast();
  final StreamController<bool> _listeningStateController =
      StreamController<bool>.broadcast();
  final StreamController<String> _statusController =
      StreamController<String>.broadcast();

  // Getters
  Stream<String> get wakeWordDetectedStream =>
      _wakeWordDetectedController.stream;
  Stream<bool> get listeningStateStream => _listeningStateController.stream;
  Stream<String> get statusStream => _statusController.stream;
  bool get isListening => _isListening;
  bool get isEnabled => _isEnabled;

  /// Sarah's wake words optimized for always-on detection
  static const List<String> wakeWords = [
    'hey younes',
    'younes',
    'fkerni', // Arabic: remind me
    'note to self',
  ];

  /// Initialize the wake word service
  Future<bool> initialize() async {
    try {
      if (_isInitialized) return true;

      // Initialize notifications
      await _initializeNotifications();

      // Check permissions
      final hasPermissions = await _checkPermissions();
      if (!hasPermissions) {
        _updateStatus('Microphone permission required');
        return false;
      }

      // Initialize speech recognition
      final speechAvailable = await _speechToText.initialize(
        onError: (error) => _handleSpeechError(error),
        onStatus: (status) => _handleSpeechStatus(status),
        debugLogging: false, // Disable for battery optimization
      );

      if (!speechAvailable) {
        _updateStatus('Speech recognition not available');
        return false;
      }

      // Load user preferences
      await _loadPreferences();

      _isInitialized = true;
      _updateStatus('Wake word service initialized');
      return true;
    } catch (e) {
      _updateStatus('Initialization failed: $e');
      return false;
    }
  }

  /// Enable always-on wake word detection
  Future<void> enableAlwaysOnListening() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      _isEnabled = true;
      await _savePreferences();
      await _startListening();

      // Enable wake lock to prevent deep sleep during critical listening
      await WakelockPlus.enable();

      _updateStatus('Always-on listening enabled');

      // Show notification
      await _showListeningNotification();

      // Start battery optimization timer
      _startBatteryOptimization();
    } catch (e) {
      _updateStatus('Failed to enable always-on listening: $e');
    }
  }

  /// Disable always-on wake word detection
  Future<void> disableAlwaysOnListening() async {
    try {
      _isEnabled = false;
      await _savePreferences();
      await _stopListening();

      // Disable wake lock
      await WakelockPlus.disable();

      _restartTimer?.cancel();
      _batteryOptimizationTimer?.cancel();

      _updateStatus('Always-on listening disabled');

      // Cancel notification
      await _notifications.cancel(1);
    } catch (e) {
      _updateStatus('Failed to disable always-on listening: $e');
    }
  }

  /// Start listening for wake words
  Future<void> _startListening() async {
    if (_isListening || !_isEnabled) return;

    try {
      await _speechToText.listen(
        onResult: (result) => _handleWakeWordResult(result),
        listenFor: const Duration(minutes: 5), // Listen for 5 minutes at a time
        pauseFor: const Duration(
          seconds: 1,
        ), // Short pause for battery optimization
        localeId: 'en_US',
        listenOptions: SpeechListenOptions(
          partialResults: true,
          onDevice: true, // Use on-device recognition for privacy
          listenMode: ListenMode.dictation,
        ),
      );

      _isListening = true;
      _listeningStateController.add(true);

      // Schedule restart to maintain continuous listening
      _scheduleRestart();
    } catch (e) {
      _updateStatus('Failed to start listening: $e');
      _scheduleRestart(); // Retry after error
    }
  }

  /// Stop listening for wake words
  Future<void> _stopListening() async {
    if (!_isListening) return;

    try {
      await _speechToText.stop();
      _isListening = false;
      _listeningStateController.add(false);
      _restartTimer?.cancel();
    } catch (e) {
      _updateStatus('Error stopping listening: $e');
    }
  }

  /// Handle wake word detection results
  void _handleWakeWordResult(SpeechRecognitionResult result) {
    final recognizedText = result.recognizedWords.toLowerCase().trim();

    if (recognizedText.isNotEmpty) {
      // Check for wake words
      for (final wakeWord in wakeWords) {
        if (recognizedText.contains(wakeWord.toLowerCase())) {
          _onWakeWordDetected(recognizedText, wakeWord);
          break;
        }
      }
    }
  }

  /// Handle wake word detection
  void _onWakeWordDetected(String fullText, String detectedWakeWord) {
    // Extract command after wake word
    final command = _extractCommand(fullText, detectedWakeWord);

    _updateStatus('Wake word detected: "$detectedWakeWord"');
    _wakeWordDetectedController.add(command);

    // Show wake word detected notification
    _showWakeWordDetectedNotification(detectedWakeWord);

    // Temporarily stop listening to process the command
    _stopListening();

    // Restart listening after a short delay
    Timer(const Duration(seconds: 3), () {
      if (_isEnabled) {
        _startListening();
      }
    });
  }

  /// Extract command from wake word text
  String _extractCommand(String fullText, String wakeWord) {
    final index = fullText.toLowerCase().indexOf(wakeWord.toLowerCase());
    if (index != -1) {
      final afterWakeWord = fullText.substring(index + wakeWord.length).trim();
      // Remove common connecting words
      return afterWakeWord
          .replaceFirst(
            RegExp(r'^(to|that|about|,)\s*', caseSensitive: false),
            '',
          )
          .trim();
    }
    return fullText;
  }

  /// Schedule restart for continuous listening
  void _scheduleRestart() {
    _restartTimer?.cancel();
    _restartTimer = Timer(const Duration(minutes: 5, seconds: 30), () {
      if (_isEnabled && !_isListening) {
        _startListening();
      }
    });
  }

  /// Start battery optimization measures
  void _startBatteryOptimization() {
    _batteryOptimizationTimer?.cancel();
    _batteryOptimizationTimer = Timer.periodic(const Duration(minutes: 10), (
      timer,
    ) {
      if (!_isEnabled) {
        timer.cancel();
        return;
      }

      // Implement battery-saving measures
      _optimizeBatteryUsage();
    });
  }

  /// Optimize battery usage during always-on listening
  void _optimizeBatteryUsage() {
    // Reduce listening sensitivity during low activity periods
    // This is a placeholder for more sophisticated battery optimization
    _updateStatus('Optimizing battery usage...');
  }

  /// Handle speech recognition errors
  void _handleSpeechError(SpeechRecognitionError error) {
    _updateStatus('Speech error: ${error.errorMsg}');
    _isListening = false;
    _listeningStateController.add(false);

    // Restart listening after error
    if (_isEnabled) {
      Timer(const Duration(seconds: 5), () {
        _startListening();
      });
    }
  }

  /// Handle speech recognition status changes
  void _handleSpeechStatus(String status) {
    if (status == 'done' || status == 'notListening') {
      _isListening = false;
      _listeningStateController.add(false);

      // Restart if still enabled
      if (_isEnabled) {
        Timer(const Duration(seconds: 2), () {
          _startListening();
        });
      }
    }
  }

  /// Check required permissions
  Future<bool> _checkPermissions() async {
    final micPermission = await Permission.microphone.status;
    final notificationPermission = await Permission.notification.status;

    if (micPermission != PermissionStatus.granted) {
      final result = await Permission.microphone.request();
      if (result != PermissionStatus.granted) return false;
    }

    if (notificationPermission != PermissionStatus.granted) {
      await Permission.notification.request();
    }

    return true;
  }

  /// Initialize notifications
  Future<void> _initializeNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(settings);
  }

  /// Show listening notification
  Future<void> _showListeningNotification() async {
    const androidDetails = AndroidNotificationDetails(
      'wake_word_channel',
      'Wake Word Detection',
      channelDescription: 'Always-on voice trigger detection',
      importance: Importance.low,
      priority: Priority.low,
      ongoing: true,
      autoCancel: false,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: false,
      presentBadge: false,
      presentSound: false,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      1,
      'EchoMind Listening',
      'Say "Hey Younes" to capture a memory',
      details,
    );
  }

  /// Show wake word detected notification
  Future<void> _showWakeWordDetectedNotification(String wakeWord) async {
    const androidDetails = AndroidNotificationDetails(
      'wake_word_detected_channel',
      'Wake Word Detected',
      channelDescription: 'Wake word detection alerts',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      2,
      'Wake Word Detected! 🎯',
      'Detected: "$wakeWord" - Starting voice recording...',
      details,
    );
  }

  /// Load user preferences
  Future<void> _loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    _isEnabled = prefs.getBool('always_on_listening') ?? false;
  }

  /// Save user preferences
  Future<void> _savePreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('always_on_listening', _isEnabled);
  }

  /// Update status message
  void _updateStatus(String status) {
    _statusController.add(status);
    if (kDebugMode) {
      // WakeWordService: $status
    }
  }

  /// Dispose resources
  void dispose() {
    _restartTimer?.cancel();
    _batteryOptimizationTimer?.cancel();
    _speechToText.stop();
    WakelockPlus.disable();

    _wakeWordDetectedController.close();
    _listeningStateController.close();
    _statusController.close();
  }
}
