import 'package:flutter/foundation.dart';
import 'dart:async';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/wake_word_service.dart';
import '../services/background_service_manager.dart';
import '../services/voice_trigger_service.dart';
import '../services/transcription_service.dart';
import '../services/gemini_service.dart';
import '../models/memory_entry.dart';

/// Provider for managing always-on voice detection
class AlwaysOnVoiceProvider with ChangeNotifier {
  final WakeWordService _wakeWordService = WakeWordService();
  final BackgroundServiceManager _backgroundService = BackgroundServiceManager();
  final VoiceTriggerService _voiceTriggerService = VoiceTriggerService();
  final TranscriptionService _transcriptionService = TranscriptionService();
  final GeminiService _geminiService = GeminiService();

  // State variables
  bool _isInitialized = false;
  bool _isAlwaysOnEnabled = false;
  bool _isBackgroundServiceRunning = false;
  bool _isProcessingWakeWord = false;
  String _currentStatus = 'Ready';
  String? _lastWakeWordCommand;
  MemoryEntry? _generatedMemory;
  
  // Battery and privacy settings
  bool _batteryOptimizationEnabled = true;
  bool _onDeviceProcessingEnabled = true;
  int _sensitivityLevel = 3; // 1-5 scale
  
  // Stream subscriptions
  StreamSubscription? _wakeWordSubscription;
  StreamSubscription? _serviceSubscription;
  StreamSubscription? _statusSubscription;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isAlwaysOnEnabled => _isAlwaysOnEnabled;
  bool get isBackgroundServiceRunning => _isBackgroundServiceRunning;
  bool get isProcessingWakeWord => _isProcessingWakeWord;
  String get currentStatus => _currentStatus;
  String? get lastWakeWordCommand => _lastWakeWordCommand;
  MemoryEntry? get generatedMemory => _generatedMemory;
  bool get batteryOptimizationEnabled => _batteryOptimizationEnabled;
  bool get onDeviceProcessingEnabled => _onDeviceProcessingEnabled;
  int get sensitivityLevel => _sensitivityLevel;

  /// Initialize always-on voice detection
  Future<bool> initialize() async {
    try {
      _updateStatus('Initializing always-on voice detection...');

      // Initialize background service
      await _backgroundService.initializeService();
      
      // Initialize wake word service
      final wakeWordInitialized = await _wakeWordService.initialize();
      if (!wakeWordInitialized) {
        _updateStatus('Wake word service initialization failed');
        return false;
      }

      // Initialize voice trigger service
      await _voiceTriggerService.initialize();

      // Load user preferences
      await _loadPreferences();

      // Set up stream listeners
      _setupStreamListeners();

      // Check if background service should auto-start
      if (_isAlwaysOnEnabled) {
        await _startBackgroundService();
      }

      _isInitialized = true;
      _updateStatus('Always-on voice detection ready');
      notifyListeners();
      return true;
    } catch (e) {
      _updateStatus('Initialization failed: $e');
      return false;
    }
  }

  /// Enable always-on voice detection
  Future<void> enableAlwaysOnDetection() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      _isAlwaysOnEnabled = true;
      await _savePreferences();
      
      // Start background service
      await _startBackgroundService();
      
      // Enable wake word service
      await _wakeWordService.enableAlwaysOnListening();
      
      _updateStatus('Always-on detection enabled 👂');
      notifyListeners();
    } catch (e) {
      _updateStatus('Failed to enable always-on detection: $e');
      _isAlwaysOnEnabled = false;
      notifyListeners();
    }
  }

  /// Disable always-on voice detection
  Future<void> disableAlwaysOnDetection() async {
    try {
      _isAlwaysOnEnabled = false;
      await _savePreferences();
      
      // Stop background service
      await _stopBackgroundService();
      
      // Disable wake word service
      await _wakeWordService.disableAlwaysOnListening();
      
      _updateStatus('Always-on detection disabled');
      notifyListeners();
    } catch (e) {
      _updateStatus('Failed to disable always-on detection: $e');
    }
  }

  /// Start background service
  Future<void> _startBackgroundService() async {
    try {
      await _backgroundService.startBackgroundService();
      _isBackgroundServiceRunning = true;
      
      // Send start listening command to background service
      final service = FlutterBackgroundService();
      service.invoke('start_listening');
      
      _updateStatus('Background service started');
      notifyListeners();
    } catch (e) {
      _updateStatus('Failed to start background service: $e');
    }
  }

  /// Stop background service
  Future<void> _stopBackgroundService() async {
    try {
      await _backgroundService.stopBackgroundService();
      _isBackgroundServiceRunning = false;
      
      _updateStatus('Background service stopped');
      notifyListeners();
    } catch (e) {
      _updateStatus('Failed to stop background service: $e');
    }
  }

  /// Process wake word command detected in background
  Future<void> processWakeWordCommand(String command) async {
    if (_isProcessingWakeWord) return;

    try {
      _isProcessingWakeWord = true;
      _lastWakeWordCommand = command;
      _updateStatus('Processing wake word: "$command"');
      notifyListeners();

      // Start voice recording session
      final recordingPath = await _voiceTriggerService.startVoiceInputSession(
        duration: const Duration(seconds: 15), // Longer for background detection
      );

      if (recordingPath != null) {
        _updateStatus('Recording voice input...');
        
        // Wait for recording to complete
        await Future.delayed(const Duration(seconds: 15));
        
        // Stop recording and get final path
        final finalPath = await _voiceTriggerService.stopVoiceInputSession();
        
        if (finalPath != null) {
          await _processRecording(finalPath, command);
        }
      }
    } catch (e) {
      _updateStatus('Error processing wake word: $e');
    } finally {
      _isProcessingWakeWord = false;
      notifyListeners();
    }
  }

  /// Process recorded audio from wake word detection
  Future<void> _processRecording(String audioPath, String wakeWordCommand) async {
    try {
      _updateStatus('Transcribing audio...');
      
      // Transcribe the audio
      final transcription = await _transcriptionService.transcribeAudio(audioPath);
      
      if (transcription.isNotEmpty) {
        _updateStatus('Generating memory...');
        
        // Combine wake word command with transcription
        final fullText = wakeWordCommand.isNotEmpty 
            ? '$wakeWordCommand $transcription'
            : transcription;
        
        // Generate memory using AI
        await _generateMemoryFromTranscription(fullText, wakeWordCommand);
        
        _updateStatus('Memory created from wake word! 💙');
      } else {
        _updateStatus('No speech detected after wake word');
      }
      
      notifyListeners();
    } catch (e) {
      _updateStatus('Error processing recording: $e');
    }
  }

  /// Generate memory entry from transcription
  Future<void> _generateMemoryFromTranscription(String transcription, String wakeWord) async {
    try {
      // Use Gemini to enhance the transcription
      final title = await _geminiService.generateTitle(transcription);
      final summary = await _geminiService.summarizeContent(transcription);
      final tags = await _geminiService.generateTags(transcription);
      final category = await _geminiService.suggestCategory(transcription);

      // Create memory entry
      _generatedMemory = MemoryEntry.create(
        userId: 'current-user', // Will be set by the service
        originalText: transcription,
        summary: title.isNotEmpty ? title : summary,
        moodTag: category.toLowerCase(),
        triggerPhrase: wakeWord,
        tags: tags,
      );

      notifyListeners();
    } catch (e) {
      // Fallback to simple memory creation
      _generatedMemory = MemoryEntry.create(
        userId: 'current-user',
        originalText: transcription,
        summary: transcription.split(' ').take(5).join(' '),
        moodTag: 'neutral',
        triggerPhrase: wakeWord,
      );
      notifyListeners();
    }
  }

  /// Update battery optimization setting
  Future<void> setBatteryOptimization(bool enabled) async {
    _batteryOptimizationEnabled = enabled;
    await _savePreferences();
    notifyListeners();
  }

  /// Update on-device processing setting
  Future<void> setOnDeviceProcessing(bool enabled) async {
    _onDeviceProcessingEnabled = enabled;
    await _savePreferences();
    notifyListeners();
  }

  /// Update sensitivity level (1-5)
  Future<void> setSensitivityLevel(int level) async {
    _sensitivityLevel = level.clamp(1, 5);
    await _savePreferences();
    notifyListeners();
  }

  /// Clear the current memory
  void clearMemory() {
    _generatedMemory = null;
    _lastWakeWordCommand = null;
    _updateStatus('Ready for next wake word');
    notifyListeners();
  }

  /// Set up stream listeners
  void _setupStreamListeners() {
    // Listen for wake word detection
    _wakeWordSubscription = _wakeWordService.wakeWordDetectedStream.listen((command) {
      processWakeWordCommand(command);
    });

    // Listen for status updates
    _statusSubscription = _wakeWordService.statusStream.listen((status) {
      _updateStatus(status);
    });

    // Listen for background service events
    final service = FlutterBackgroundService();
    service.on('wake_word_detected').listen((event) {
      final command = event?['command'] as String? ?? '';
      processWakeWordCommand(command);
    });

    service.on('launch_app').listen((event) {
      final command = event?['command'] as String? ?? '';
      processWakeWordCommand(command);
    });
  }

  /// Load user preferences
  Future<void> _loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    _isAlwaysOnEnabled = prefs.getBool('always_on_enabled') ?? false;
    _batteryOptimizationEnabled = prefs.getBool('battery_optimization') ?? true;
    _onDeviceProcessingEnabled = prefs.getBool('on_device_processing') ?? true;
    _sensitivityLevel = prefs.getInt('sensitivity_level') ?? 3;
  }

  /// Save user preferences
  Future<void> _savePreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('always_on_enabled', _isAlwaysOnEnabled);
    await prefs.setBool('battery_optimization', _batteryOptimizationEnabled);
    await prefs.setBool('on_device_processing', _onDeviceProcessingEnabled);
    await prefs.setInt('sensitivity_level', _sensitivityLevel);
  }

  /// Update status message
  void _updateStatus(String status) {
    _currentStatus = status;
    // AlwaysOnVoiceProvider: $status
  }

  /// Get battery usage estimate
  String getBatteryUsageEstimate() {
    if (!_isAlwaysOnEnabled) return 'Disabled';
    
    if (_batteryOptimizationEnabled) {
      return 'Low (~2-5% per day)';
    } else {
      return 'Medium (~5-10% per day)';
    }
  }

  /// Get privacy level description
  String getPrivacyLevel() {
    if (_onDeviceProcessingEnabled) {
      return 'High - All processing on device';
    } else {
      return 'Medium - Cloud processing for accuracy';
    }
  }

  @override
  void dispose() {
    _wakeWordSubscription?.cancel();
    _serviceSubscription?.cancel();
    _statusSubscription?.cancel();
    _wakeWordService.dispose();
    super.dispose();
  }
}
