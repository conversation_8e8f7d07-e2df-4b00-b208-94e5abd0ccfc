import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/app_state.dart';
import '../models/memory_entry.dart';
import '../services/supabase_service.dart';
import '../services/audio_service.dart';
import '../services/transcription_service.dart';
import '../services/gemini_service.dart';

class AppStateProvider extends ChangeNotifier {
  AppState _state = const AppState();

  // Services
  final SupabaseService _supabaseService = SupabaseService();
  final AudioService _audioService = AudioService();
  final TranscriptionService _transcriptionService = TranscriptionService();
  final GeminiService _geminiService = GeminiService();

  // Getters
  AppState get state => _state;
  bool get isLoading => _state.isLoading;
  bool get hasError => _state.hasError;
  String? get errorMessage => _state.errorMessage;
  List<MemoryEntry> get memories => _state.memories;
  List<MemoryEntry> get filteredMemories => _state.filteredMemories;
  User? get currentUser => _state.user;
  bool get isAuthenticated => _state.isAuthenticated;
  bool get isRecording => _state.isRecording;
  RecordingStatus get recordingStatus => _state.recordingStatus;

  /// Initialize the app
  Future<void> initialize() async {
    try {
      _updateState(_state.copyWith(status: AppStatus.loading));

      // Initialize services
      await _supabaseService.initialize();
      await _audioService.initialize();

      // Check for existing user session
      final user = _supabaseService.getCurrentUser();
      if (user != null) {
        _updateState(_state.copyWith(user: user));
        await loadMemories();
      }

      _updateState(_state.copyWith(status: AppStatus.loaded));
    } catch (e) {
      _updateState(
        _state.copyWith(status: AppStatus.error, errorMessage: e.toString()),
      );
    }
  }

  /// Sign in user
  Future<bool> signIn(String email, String password) async {
    try {
      _updateState(_state.copyWith(status: AppStatus.loading));

      final response = await _supabaseService.signIn(email, password);
      if (response.user != null) {
        _updateState(
          _state.copyWith(user: response.user, status: AppStatus.loaded),
        );
        await loadMemories();
        return true;
      }
      return false;
    } catch (e) {
      _updateState(
        _state.copyWith(status: AppStatus.error, errorMessage: e.toString()),
      );
      return false;
    }
  }

  /// Sign up user
  Future<bool> signUp(String email, String password) async {
    try {
      _updateState(_state.copyWith(status: AppStatus.loading));

      final response = await _supabaseService.signUp(email, password);
      if (response.user != null) {
        _updateState(
          _state.copyWith(user: response.user, status: AppStatus.loaded),
        );
        return true;
      }
      return false;
    } catch (e) {
      _updateState(
        _state.copyWith(status: AppStatus.error, errorMessage: e.toString()),
      );
      return false;
    }
  }

  /// Sign out user
  Future<void> signOut() async {
    try {
      await _supabaseService.signOut();
      _updateState(const AppState(status: AppStatus.loaded));
    } catch (e) {
      _updateState(
        _state.copyWith(status: AppStatus.error, errorMessage: e.toString()),
      );
    }
  }

  /// Load memories from storage
  Future<void> loadMemories() async {
    try {
      _updateState(_state.copyWith(status: AppStatus.loading));

      final memories = await _supabaseService.getMemories();
      _updateState(
        _state.copyWith(
          memories: memories,
          filteredMemories: memories,
          status: AppStatus.loaded,
        ),
      );
    } catch (e) {
      _updateState(
        _state.copyWith(status: AppStatus.error, errorMessage: e.toString()),
      );
    }
  }

  /// Save a new memory
  Future<bool> saveMemory(MemoryEntry memory) async {
    try {
      final savedMemory = await _supabaseService.saveMemory(memory);

      final updatedMemories = [..._state.memories, savedMemory];
      _updateState(
        _state.copyWith(
          memories: updatedMemories,
          filteredMemories: _filterMemories(updatedMemories),
        ),
      );

      return true;
    } catch (e) {
      _updateState(
        _state.copyWith(status: AppStatus.error, errorMessage: e.toString()),
      );
      return false;
    }
  }

  /// Update an existing memory
  Future<bool> updateMemory(MemoryEntry memory) async {
    try {
      final updatedMemory = await _supabaseService.updateMemory(memory);

      final updatedMemories = _state.memories.map((m) {
        return m.id == updatedMemory.id ? updatedMemory : m;
      }).toList();

      _updateState(
        _state.copyWith(
          memories: updatedMemories,
          filteredMemories: _filterMemories(updatedMemories),
        ),
      );

      return true;
    } catch (e) {
      _updateState(
        _state.copyWith(status: AppStatus.error, errorMessage: e.toString()),
      );
      return false;
    }
  }

  /// Delete a memory
  Future<bool> deleteMemory(String memoryId) async {
    try {
      await _supabaseService.deleteMemory(memoryId);

      final updatedMemories = _state.memories
          .where((m) => m.id != memoryId)
          .toList();

      _updateState(
        _state.copyWith(
          memories: updatedMemories,
          filteredMemories: _filterMemories(updatedMemories),
        ),
      );

      return true;
    } catch (e) {
      _updateState(
        _state.copyWith(status: AppStatus.error, errorMessage: e.toString()),
      );
      return false;
    }
  }

  /// Toggle favorite status of a memory
  Future<void> toggleFavorite(String memoryId) async {
    final memory = _state.memories.firstWhere((m) => m.id == memoryId);
    final updatedMemory = memory.copyWith(isFavorite: !memory.isFavorite);
    await updateMemory(updatedMemory);
  }

  /// Search memories
  void searchMemories(String query) {
    _updateState(_state.copyWith(searchQuery: query));
    final filteredMemories = _filterMemories(_state.memories);
    _updateState(_state.copyWith(filteredMemories: filteredMemories));
  }

  /// Filter memories by category
  void filterByCategory(String category) {
    _updateState(_state.copyWith(selectedCategory: category));
    final filteredMemories = _filterMemories(_state.memories);
    _updateState(_state.copyWith(filteredMemories: filteredMemories));
  }

  /// Clear search and filters
  void clearFilters() {
    _updateState(
      _state.copyWith(
        searchQuery: '',
        selectedCategory: 'All',
        filteredMemories: _state.memories,
      ),
    );
  }

  /// Start recording
  Future<void> startRecording() async {
    try {
      _updateState(
        _state.copyWith(
          isRecording: true,
          recordingStatus: RecordingStatus.recording,
        ),
      );

      await _audioService.startRecording();
    } catch (e) {
      _updateState(
        _state.copyWith(
          isRecording: false,
          recordingStatus: RecordingStatus.error,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  /// Stop recording and process
  Future<String?> stopRecording() async {
    try {
      _updateState(
        _state.copyWith(
          isRecording: false,
          recordingStatus: RecordingStatus.processing,
        ),
      );

      final audioPath = await _audioService.stopRecording();
      if (audioPath != null) {
        final transcription = await _transcriptionService.transcribeAudio(
          audioPath,
        );

        _updateState(
          _state.copyWith(recordingStatus: RecordingStatus.completed),
        );

        return transcription;
      }

      _updateState(
        _state.copyWith(
          recordingStatus: RecordingStatus.error,
          errorMessage: 'Failed to save recording',
        ),
      );

      return null;
    } catch (e) {
      _updateState(
        _state.copyWith(
          recordingStatus: RecordingStatus.error,
          errorMessage: e.toString(),
        ),
      );
      return null;
    }
  }

  /// Generate AI insights for memory
  Future<void> generateInsights(String memoryId) async {
    try {
      final memory = _state.memories.firstWhere((m) => m.id == memoryId);
      final insights = await _geminiService.generateInsights(memory);

      // Update memory with insights
      final updatedMemory = memory.copyWith(
        metadata: {...?memory.metadata, 'insights': insights.toJson()},
      );

      await updateMemory(updatedMemory);
    } catch (e) {
      _updateState(
        _state.copyWith(status: AppStatus.error, errorMessage: e.toString()),
      );
    }
  }

  /// Clear error state
  void clearError() {
    _updateState(_state.copyWith(status: AppStatus.loaded, errorMessage: null));
  }

  /// Filter memories based on current search and category filters
  List<MemoryEntry> _filterMemories(List<MemoryEntry> memories) {
    return memories.where((memory) {
      // Category filter
      if (_state.selectedCategory != 'All' &&
          memory.category != _state.selectedCategory) {
        return false;
      }

      // Search filter
      if (_state.searchQuery.isNotEmpty) {
        final query = _state.searchQuery.toLowerCase();
        return memory.title.toLowerCase().contains(query) ||
            memory.content.toLowerCase().contains(query) ||
            memory.tags.any((tag) => tag.toLowerCase().contains(query));
      }

      return true;
    }).toList();
  }

  /// Update state and notify listeners
  void _updateState(AppState newState) {
    _state = newState;
    notifyListeners();
  }

  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }
}
