import 'package:flutter/foundation.dart';
import 'dart:async';
import '../models/memory_entry.dart';
import '../services/audio_service.dart';
import '../services/transcription_service.dart';
import '../services/gemini_service.dart';

enum RecordingState { idle, recording, processing, completed, error }

class RecordingProvider extends ChangeNotifier {
  final AudioService _audioService = AudioService();
  final TranscriptionService _transcriptionService = TranscriptionService();
  final GeminiService _geminiService = GeminiService();

  RecordingState _state = RecordingState.idle;
  Duration _duration = Duration.zero;
  String _transcription = '';
  String _suggestedTitle = '';
  String _suggestedCategory = '';
  List<String> _suggestedTags = [];
  String? _audioFilePath;
  String? _errorMessage;

  StreamSubscription<Duration>? _durationSubscription;
  StreamSubscription<bool>? _recordingStateSubscription;

  // Getters
  RecordingState get state => _state;
  Duration get duration => _duration;
  String get transcription => _transcription;
  String get suggestedTitle => _suggestedTitle;
  String get suggestedCategory => _suggestedCategory;
  List<String> get suggestedTags => _suggestedTags;
  String? get audioFilePath => _audioFilePath;
  String? get errorMessage => _errorMessage;

  bool get isRecording => _state == RecordingState.recording;
  bool get isProcessing => _state == RecordingState.processing;
  bool get isCompleted => _state == RecordingState.completed;
  bool get hasError => _state == RecordingState.error;
  bool get hasTranscription => _transcription.isNotEmpty;

  RecordingProvider() {
    _initializeListeners();
  }

  void _initializeListeners() {
    // Listen to recording duration updates
    _durationSubscription = _audioService.recordingDurationStream.listen((
      duration,
    ) {
      _duration = duration;
      notifyListeners();
    });

    // Listen to recording state changes
    _recordingStateSubscription = _audioService.recordingStateStream.listen((
      isRecording,
    ) {
      if (!isRecording && _state == RecordingState.recording) {
        _processRecording();
      }
    });
  }

  /// Start recording
  Future<void> startRecording() async {
    try {
      _setState(RecordingState.recording);
      _clearData();

      _audioFilePath = await _audioService.startRecording();

      if (_audioFilePath == null) {
        throw Exception('Failed to start recording');
      }
    } catch (e) {
      _setError('Failed to start recording: ${e.toString()}');
    }
  }

  /// Stop recording
  Future<void> stopRecording() async {
    try {
      if (_state != RecordingState.recording) return;

      _audioFilePath = await _audioService.stopRecording();

      if (_audioFilePath != null) {
        await _processRecording();
      } else {
        throw Exception('Failed to save recording');
      }
    } catch (e) {
      _setError('Failed to stop recording: ${e.toString()}');
    }
  }

  /// Process the recorded audio
  Future<void> _processRecording() async {
    try {
      if (_audioFilePath == null) {
        throw Exception('No audio file to process');
      }

      _setState(RecordingState.processing);

      // Transcribe audio
      _transcription = await _transcriptionService.transcribeAudio(
        _audioFilePath!,
      );
      notifyListeners();

      // Generate AI suggestions if transcription is available
      if (_transcription.isNotEmpty) {
        await _generateAISuggestions();
      }

      _setState(RecordingState.completed);
    } catch (e) {
      _setError('Failed to process recording: ${e.toString()}');
    }
  }

  /// Generate AI suggestions for title, category, and tags
  Future<void> _generateAISuggestions() async {
    try {
      // Generate suggestions in parallel
      final futures = await Future.wait([
        _geminiService.generateTitle(_transcription),
        _geminiService.suggestCategory(_transcription),
        _geminiService.generateTags(_transcription),
      ]);

      _suggestedTitle = futures[0] as String;
      _suggestedCategory = futures[1] as String;
      _suggestedTags = futures[2] as List<String>;

      notifyListeners();
    } catch (e) {
      // AI suggestions are optional, so we don't treat this as a critical error
      debugPrint('Failed to generate AI suggestions: $e');
    }
  }

  /// Create memory entry from current recording
  MemoryEntry createMemoryEntry({
    String? customTitle,
    String? customCategory,
    List<String>? customTags,
  }) {
    if (_transcription.isEmpty) {
      throw Exception('No transcription available');
    }

    final title = customTitle?.isNotEmpty == true
        ? customTitle!
        : _suggestedTitle.isNotEmpty
        ? _suggestedTitle
        : 'Voice Memory';

    final category = customCategory?.isNotEmpty == true
        ? customCategory!
        : _suggestedCategory.isNotEmpty
        ? _suggestedCategory
        : 'Personal';

    final tags = customTags?.isNotEmpty == true ? customTags! : _suggestedTags;

    return MemoryEntry.create(
      userId: 'current-user', // This will be set by the service
      originalText: _transcription,
      summary: title,
      moodTag: category.toLowerCase(),
      triggerPhrase: 'voice recording',
      audioFilePath: _audioFilePath,
      tags: tags,
      audioDuration: _duration.inSeconds.toDouble(),
    );
  }

  /// Retry transcription
  Future<void> retryTranscription() async {
    if (_audioFilePath == null) {
      _setError('No audio file available for transcription');
      return;
    }

    try {
      _setState(RecordingState.processing);
      _transcription = await _transcriptionService.transcribeAudio(
        _audioFilePath!,
      );

      if (_transcription.isNotEmpty) {
        await _generateAISuggestions();
        _setState(RecordingState.completed);
      } else {
        throw Exception('Transcription returned empty result');
      }
    } catch (e) {
      _setError('Failed to retry transcription: ${e.toString()}');
    }
  }

  /// Clear current recording data
  void clearRecording() {
    _clearData();
    _setState(RecordingState.idle);
  }

  /// Update transcription manually
  void updateTranscription(String newTranscription) {
    _transcription = newTranscription;
    notifyListeners();

    // Regenerate AI suggestions with new transcription
    if (_transcription.isNotEmpty) {
      _generateAISuggestions();
    }
  }

  /// Check if microphone permission is available
  Future<bool> checkMicrophonePermission() async {
    try {
      return await _audioService.checkPermission();
    } catch (e) {
      return false;
    }
  }

  /// Request microphone permission
  Future<bool> requestMicrophonePermission() async {
    try {
      return await _audioService.requestPermission();
    } catch (e) {
      return false;
    }
  }

  /// Get recording duration as formatted string
  String get formattedDuration {
    final minutes = _duration.inMinutes;
    final seconds = _duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Check if recording is within time limits
  bool get isWithinTimeLimit {
    const maxDuration = Duration(minutes: 5); // 5 minutes max
    return _duration <= maxDuration;
  }

  /// Get remaining recording time
  Duration get remainingTime {
    const maxDuration = Duration(minutes: 5);
    final remaining = maxDuration - _duration;
    return remaining.isNegative ? Duration.zero : remaining;
  }

  void _setState(RecordingState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = RecordingState.error;
    _errorMessage = error;
    notifyListeners();
  }

  void _clearData() {
    _duration = Duration.zero;
    _transcription = '';
    _suggestedTitle = '';
    _suggestedCategory = '';
    _suggestedTags = [];
    _audioFilePath = null;
    _errorMessage = null;
  }

  @override
  void dispose() {
    _durationSubscription?.cancel();
    _recordingStateSubscription?.cancel();
    super.dispose();
  }
}
