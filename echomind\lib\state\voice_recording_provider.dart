import 'package:flutter/foundation.dart';
import 'dart:async';
import '../services/voice_trigger_service.dart';
import '../services/audio_service.dart';
import '../services/transcription_service.dart';
import '../services/gemini_service.dart';
import '../models/memory_entry.dart';

/// Provider for managing voice recording with trigger phrase detection
class VoiceRecordingProvider with ChangeNotifier {
  final VoiceTriggerService _voiceTriggerService = VoiceTriggerService();
  final AudioService _audioService = AudioService();
  final TranscriptionService _transcriptionService = TranscriptionService();
  final GeminiService _geminiService = GeminiService();

  // State variables
  bool _isInitialized = false;
  bool _isListeningForTrigger = false;
  bool _isRecordingVoiceInput = false;
  String _currentStatus = 'Ready';
  String? _lastDetectedTrigger;
  Duration _recordingDuration = Duration.zero;
  String? _transcribedText;
  MemoryEntry? _generatedMemory;

  // Stream subscriptions
  StreamSubscription? _triggerSubscription;
  StreamSubscription? _recordingStateSubscription;
  StreamSubscription? _recordingDurationSubscription;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isListeningForTrigger => _isListeningForTrigger;
  bool get isRecordingVoiceInput => _isRecordingVoiceInput;
  String get currentStatus => _currentStatus;
  String? get lastDetectedTrigger => _lastDetectedTrigger;
  Duration get recordingDuration => _recordingDuration;
  String? get transcribedText => _transcribedText;
  MemoryEntry? get generatedMemory => _generatedMemory;

  /// Initialize the voice recording system
  Future<bool> initialize() async {
    try {
      _updateStatus('Initializing voice services...');

      // Initialize all services
      final voiceInitialized = await _voiceTriggerService.initialize();
      await _audioService.initialize();

      if (!voiceInitialized) {
        _updateStatus('Voice recognition not available');
        return false;
      }

      // Set up stream listeners
      _setupStreamListeners();

      _isInitialized = true;
      _updateStatus('Ready - Say "Hey Younes" to start');
      notifyListeners();
      return true;
    } catch (e) {
      _updateStatus('Initialization failed: $e');
      return false;
    }
  }

  /// Start listening for trigger phrases
  Future<void> startTriggerListening() async {
    if (!_isInitialized || _isListeningForTrigger) return;

    try {
      await _voiceTriggerService.startTriggerListening();
      _isListeningForTrigger = true;
      _updateStatus('Listening for "Hey Younes..." 👂');
      notifyListeners();
    } catch (e) {
      _updateStatus('Failed to start listening: $e');
    }
  }

  /// Stop listening for trigger phrases
  Future<void> stopTriggerListening() async {
    if (!_isListeningForTrigger) return;

    try {
      await _voiceTriggerService.stopTriggerListening();
      _isListeningForTrigger = false;
      _updateStatus('Stopped listening');
      notifyListeners();
    } catch (e) {
      _updateStatus('Error stopping listening: $e');
    }
  }

  /// Manually start voice input session (for testing)
  Future<void> startManualVoiceInput() async {
    if (!_isInitialized || _isRecordingVoiceInput) return;

    try {
      _updateStatus('Starting voice input session...');
      final path = await _voiceTriggerService.startVoiceInputSession();

      if (path != null) {
        _isRecordingVoiceInput = true;
        _updateStatus('Recording... Speak now! 🎤');
        notifyListeners();
      }
    } catch (e) {
      _updateStatus('Failed to start recording: $e');
    }
  }

  /// Stop voice input session
  Future<void> stopVoiceInput() async {
    if (!_isRecordingVoiceInput) return;

    try {
      _updateStatus('Processing voice input...');
      final path = await _voiceTriggerService.stopVoiceInputSession();

      _isRecordingVoiceInput = false;
      _recordingDuration = Duration.zero;
      notifyListeners();

      if (path != null) {
        await _processRecording(path);
      }
    } catch (e) {
      _updateStatus('Error processing recording: $e');
    }
  }

  /// Process the recorded audio file
  Future<void> _processRecording(String audioPath) async {
    try {
      _updateStatus('Transcribing audio... 🔄');

      // Transcribe the audio
      final transcription = await _transcriptionService.transcribeAudio(
        audioPath,
      );
      _transcribedText = transcription;

      if (transcription.isNotEmpty) {
        _updateStatus('Generating memory... ✨');

        // Generate memory using AI
        await _generateMemoryFromTranscription(transcription);

        _updateStatus('Memory created! 💙');
      } else {
        _updateStatus('No speech detected');
      }

      notifyListeners();
    } catch (e) {
      _updateStatus('Error processing recording: $e');
    }
  }

  /// Generate memory entry from transcription
  Future<void> _generateMemoryFromTranscription(String transcription) async {
    try {
      // Use Gemini to create a concise, helpful memory summary
      final summary = await _geminiService.summarizeMemory(transcription);
      final title = await _geminiService.generateTitle(transcription);
      final tags = await _geminiService.generateTags(transcription);
      final category = await _geminiService.suggestCategory(transcription);

      // Create memory entry with AI-generated summary
      _generatedMemory = MemoryEntry.create(
        userId: 'current-user', // Will be set by the service
        originalText: transcription,
        summary: summary.isNotEmpty ? summary : title,
        moodTag: category.toLowerCase(),
        triggerPhrase: _lastDetectedTrigger ?? 'voice input',
        tags: tags,
      );

      notifyListeners();
    } catch (e) {
      // Fallback to simple memory creation
      _generatedMemory = MemoryEntry.create(
        userId: 'current-user',
        originalText: transcription,
        summary: transcription.split(' ').take(8).join(' '),
        moodTag: 'neutral',
        triggerPhrase: _lastDetectedTrigger ?? 'voice input',
      );
      notifyListeners();
    }
  }

  /// Clear the current memory
  void clearMemory() {
    _generatedMemory = null;
    _transcribedText = null;
    _lastDetectedTrigger = null;
    _updateStatus('Ready - Say "Hey Younes" to start');
    notifyListeners();
  }

  /// Set up stream listeners for voice services
  void _setupStreamListeners() {
    // Listen for trigger phrase detection
    _triggerSubscription = _voiceTriggerService.triggerDetectedStream.listen((
      command,
    ) {
      _lastDetectedTrigger = command;
      _updateStatus('Trigger detected: "$command" 🎯');
      notifyListeners();
    });

    // Listen for recording state changes
    _recordingStateSubscription = _voiceTriggerService.recordingStateStream
        .listen((isRecording) {
          _isRecordingVoiceInput = isRecording;
          if (isRecording) {
            _updateStatus('Recording voice input... 🎤');
          }
          notifyListeners();
        });

    // Listen for recording duration updates
    _recordingDurationSubscription = _voiceTriggerService
        .recordingDurationStream
        .listen((duration) {
          _recordingDuration = duration;
          notifyListeners();
        });
  }

  /// Update status message
  void _updateStatus(String status) {
    _currentStatus = status;
    // VoiceRecordingProvider: $status
  }

  /// Get formatted recording duration
  String get formattedDuration {
    final minutes = _recordingDuration.inMinutes;
    final seconds = _recordingDuration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Check if trigger phrase is detected in text
  bool containsTriggerPhrase(String text) {
    return _voiceTriggerService.containsTriggerPhrase(text);
  }

  /// Extract command from triggered text
  String extractCommand(String text) {
    return _voiceTriggerService.extractCommand(text);
  }

  @override
  void dispose() {
    _triggerSubscription?.cancel();
    _recordingStateSubscription?.cancel();
    _recordingDurationSubscription?.cancel();
    _voiceTriggerService.dispose();
    super.dispose();
  }
}
