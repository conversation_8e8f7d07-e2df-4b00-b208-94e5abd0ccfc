import 'package:flutter/material.dart';
import '../core/app_constants.dart';
import 'custom_button.dart';

class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final String? actionText;
  final VoidCallback? onActionPressed;
  final Color? iconColor;
  final double iconSize;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.actionText,
    this.onActionPressed,
    this.iconColor,
    this.iconSize = 64.0,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.largePadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: iconColor ?? Colors.grey[400],
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (actionText != null && onActionPressed != null) ...[
              const SizedBox(height: AppConstants.largePadding),
              PrimaryButton(
                text: actionText!,
                onPressed: onActionPressed,
                icon: _getActionIcon(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData? _getActionIcon() {
    if (actionText?.toLowerCase().contains('record') == true) {
      return Icons.mic;
    } else if (actionText?.toLowerCase().contains('add') == true) {
      return Icons.add;
    } else if (actionText?.toLowerCase().contains('create') == true) {
      return Icons.create;
    }
    return null;
  }
}

// Predefined empty states for common scenarios
class NoMemoriesEmptyState extends EmptyStateWidget {
  const NoMemoriesEmptyState({
    super.key,
    super.onActionPressed,
  }) : super(
          icon: Icons.memory,
          title: 'No memories yet',
          subtitle: 'Start by recording your first memory to capture your thoughts and ideas.',
          actionText: 'Record Memory',
        );
}

class NoSearchResultsEmptyState extends EmptyStateWidget {
  const NoSearchResultsEmptyState({
    super.key,
    super.onActionPressed,
  }) : super(
          icon: Icons.search_off,
          title: 'No results found',
          subtitle: 'Try adjusting your search terms or filters to find what you\'re looking for.',
          actionText: 'Clear Filters',
        );
}

class NoFavoritesEmptyState extends EmptyStateWidget {
  const NoFavoritesEmptyState({
    super.key,
    super.onActionPressed,
  }) : super(
          icon: Icons.favorite_border,
          title: 'No favorites yet',
          subtitle: 'Mark memories as favorites by tapping the heart icon to find them easily later.',
          actionText: 'Browse Memories',
        );
}

class NetworkErrorEmptyState extends EmptyStateWidget {
  const NetworkErrorEmptyState({
    super.key,
    super.onActionPressed,
  }) : super(
          icon: Icons.wifi_off,
          title: 'Connection error',
          subtitle: 'Please check your internet connection and try again.',
          actionText: 'Retry',
          iconColor: Colors.orange,
        );
}

class ErrorEmptyState extends EmptyStateWidget {
  const ErrorEmptyState({
    super.key,
    String? errorMessage,
    super.onActionPressed,
  }) : super(
          icon: Icons.error_outline,
          title: 'Something went wrong',
          subtitle: errorMessage ?? 'An unexpected error occurred. Please try again.',
          actionText: 'Retry',
          iconColor: Colors.red,
        );
}

class LoadingEmptyState extends StatefulWidget {
  final String title;
  final String subtitle;

  const LoadingEmptyState({
    super.key,
    this.title = 'Loading...',
    this.subtitle = 'Please wait while we fetch your data.',
  });

  @override
  State<LoadingEmptyState> createState() => _LoadingEmptyStateState();
}

class _LoadingEmptyStateState extends State<LoadingEmptyState>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.largePadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _animation.value * 2 * 3.14159,
                  child: Icon(
                    Icons.refresh,
                    size: 64,
                    color: Theme.of(context).primaryColor,
                  ),
                );
              },
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              widget.title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              widget.subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
