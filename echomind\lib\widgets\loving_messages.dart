import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../core/app_theme.dart';

class LovingMessages {
  static const List<String> saveMessages = [
    "Memory saved 💙",
    "Your thoughts are precious ✨",
    "Another beautiful memory captured 🩷",
    "Voice of the Engineer: recorded 💖",
    "Safely stored in your heart-drive 💾❤️",
    "Memory chip updated with love 💕",
    "Your brilliant mind documented 🧠✨",
    "Another piece of your story saved 📖💙",
    "Engineering your memories with care 🔧💕",
    "Data saved with extra love 💝",
  ];

  static const List<String> recordingMessages = [
    "Listening to your beautiful voice... 🎤💙",
    "Capturing your thoughts with love 💕",
    "Your voice matters, Sarah 🩷",
    "Recording your brilliant ideas ✨",
    "Every word is precious 💖",
    "Your engineer's mind at work 🧠💙",
    "Documenting your genius 📝💕",
    "Voice patterns: uniquely yours 🎵💙",
    "Recording with romantic precision 💝",
    "Your thoughts deserve to be heard 🩷",
  ];

  static const List<String> welcomeMessages = [
    "Welcome back, brilliant engineer 💙",
    "Ready to capture more memories? 🩷",
    "Your digital diary awaits ✨",
    "Time to document your genius 💖",
    "Hello, beautiful mind 🧠💕",
    "Your memory palace is ready 🏰💙",
    "Engineering memories with love 💝",
    "Ready for another adventure? 🌟",
    "Your thoughts are waiting 💭💙",
    "Let's create something beautiful 🩷",
  ];

  static const List<String> emptyStateMessages = [
    "Your first memory awaits 💙",
    "Start your beautiful journey ✨",
    "Every engineer needs a diary 📖💕",
    "Your voice deserves to be heard 🎤💙",
    "Begin documenting your brilliance 🧠💖",
    "Create your first digital memory 💝",
    "Your story starts here 🌟",
    "Ready to capture your thoughts? 🩷",
    "Let's build your memory collection 🏗️💙",
    "Your engineering journey begins 💕",
  ];

  static String getRandomMessage(List<String> messages) {
    final random = math.Random();
    return messages[random.nextInt(messages.length)];
  }

  static String getSaveMessage() => getRandomMessage(saveMessages);
  static String getRecordingMessage() => getRandomMessage(recordingMessages);
  static String getWelcomeMessage() => getRandomMessage(welcomeMessages);
  static String getEmptyStateMessage() => getRandomMessage(emptyStateMessages);
}

class RomanticSnackBar extends StatelessWidget {
  final String message;
  final bool isSuccess;
  final bool isError;

  const RomanticSnackBar({
    super.key,
    required this.message,
    this.isSuccess = true,
    this.isError = false,
  });

  @override
  Widget build(BuildContext context) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    if (isError) {
      backgroundColor = AppColors.errorRose;
      textColor = AppColors.pureWhite;
      icon = Icons.error_outline;
    } else if (isSuccess) {
      backgroundColor = AppColors.successGreen;
      textColor = AppColors.pureWhite;
      icon = Icons.check_circle_outline;
    } else {
      backgroundColor = AppColors.lightBlue;
      textColor = AppColors.textPrimary;
      icon = Icons.info_outline;
    }

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withOpacity(0.3),
            blurRadius: 10,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: textColor, size: 20),
          const SizedBox(width: 12),
          Flexible(
            child: Text(
              message,
              style: TextStyle(
                color: textColor,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  static void show(BuildContext context, String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: RomanticSnackBar(
          message: message,
          isError: isError,
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}

class PinkShimmerEffect extends StatefulWidget {
  final Widget child;
  final bool isActive;
  final Duration duration;

  const PinkShimmerEffect({
    super.key,
    required this.child,
    this.isActive = false,
    this.duration = const Duration(milliseconds: 1500),
  });

  @override
  State<PinkShimmerEffect> createState() => _PinkShimmerEffectState();
}

class _PinkShimmerEffectState extends State<PinkShimmerEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.isActive) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(PinkShimmerEffect oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.repeat();
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isActive) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                Colors.transparent,
                AppColors.softBlushPink.withOpacity(0.6),
                AppColors.pinkGlow.withOpacity(0.4),
                AppColors.softBlushPink.withOpacity(0.6),
                Colors.transparent,
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value - 0.1,
                _animation.value,
                _animation.value + 0.1,
                _animation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

class FloatingHearts extends StatefulWidget {
  final bool isActive;
  final int heartCount;

  const FloatingHearts({
    super.key,
    this.isActive = false,
    this.heartCount = 5,
  });

  @override
  State<FloatingHearts> createState() => _FloatingHeartsState();
}

class _FloatingHeartsState extends State<FloatingHearts>
    with TickerProviderStateMixin {
  List<AnimationController> _controllers = [];
  List<Animation<Offset>> _animations = [];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _controllers = List.generate(widget.heartCount, (index) {
      return AnimationController(
        duration: Duration(milliseconds: 2000 + (index * 200)),
        vsync: this,
      );
    });

    _animations = _controllers.map((controller) {
      return Tween<Offset>(
        begin: const Offset(0, 1),
        end: const Offset(0, -1),
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOut,
      ));
    }).toList();

    if (widget.isActive) {
      _startAnimations();
    }
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 300), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void didUpdateWidget(FloatingHearts oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _startAnimations();
      } else {
        for (var controller in _controllers) {
          controller.reset();
        }
      }
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isActive) {
      return const SizedBox.shrink();
    }

    return Stack(
      children: List.generate(widget.heartCount, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Positioned(
              left: 50.0 + (index * 30.0),
              child: SlideTransition(
                position: _animations[index],
                child: Opacity(
                  opacity: 1.0 - _controllers[index].value,
                  child: Icon(
                    Icons.favorite,
                    color: AppColors.loveNotes.withOpacity(0.8),
                    size: 16 + (index * 2.0),
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }
}

class LovingLoadingIndicator extends StatefulWidget {
  final String message;
  final double size;

  const LovingLoadingIndicator({
    super.key,
    this.message = "Processing with love...",
    this.size = 40.0,
  });

  @override
  State<LovingLoadingIndicator> createState() => _LovingLoadingIndicatorState();
}

class _LovingLoadingIndicatorState extends State<LovingLoadingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _pulseAnimation.value,
              child: AnimatedBuilder(
                animation: _rotationController,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _rotationController.value * 2 * math.pi,
                    child: Container(
                      width: widget.size,
                      height: widget.size,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [
                            AppColors.lightBlue,
                            AppColors.softBlushPink,
                            AppColors.gentleMint,
                          ],
                        ),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.favorite,
                        color: AppColors.pureWhite,
                        size: 20,
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
        const SizedBox(height: 16),
        Text(
          widget.message,
          style: const TextStyle(
            color: AppColors.textSecondary,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
