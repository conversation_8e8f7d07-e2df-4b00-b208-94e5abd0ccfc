import 'package:flutter/material.dart';
import '../core/app_theme.dart';
import '../core/app_utils.dart';
import '../models/memory_entry.dart';
import 'romantic_voice_button.dart';

enum MemoryMood { calm, emotional, focused, love, study, creative }

class RomanticMemoryCard extends StatefulWidget {
  final MemoryEntry memory;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteToggle;
  final VoidCallback? onDelete;
  final MemoryMood mood;

  const RomanticMemoryCard({
    super.key,
    required this.memory,
    this.onTap,
    this.onFavoriteToggle,
    this.onDelete,
    this.mood = MemoryMood.calm,
  });

  @override
  State<RomanticMemoryCard> createState() => _RomanticMemoryCardState();
}

class _RomanticMemoryCardState extends State<RomanticMemoryCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _shimmerController;
  late Animation<double> _shimmerAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _shimmerAnimation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _shimmerController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    super.dispose();
  }

  void _triggerShimmer() {
    _shimmerController.forward().then((_) {
      _shimmerController.reset();
    });
  }

  Color get _moodColor {
    switch (widget.mood) {
      case MemoryMood.calm:
        return AppColors.calmBlue;
      case MemoryMood.emotional:
        return AppColors.emotionalPink;
      case MemoryMood.focused:
        return AppColors.focusedMint;
      case MemoryMood.love:
        return AppColors.loveNotes;
      case MemoryMood.study:
        return AppColors.studyMode;
      case MemoryMood.creative:
        return AppColors.gentleMint;
    }
  }

  IconData get _moodIcon {
    switch (widget.mood) {
      case MemoryMood.calm:
        return Icons.spa; // 🧘‍♀️ Zen/calm
      case MemoryMood.emotional:
        return Icons.favorite; // 💖 Heart
      case MemoryMood.focused:
        return Icons.psychology; // 🧠 Brain/focus
      case MemoryMood.love:
        return Icons.favorite_rounded; // 💕 Love
      case MemoryMood.study:
        return Icons.school; // 📚 Study
      case MemoryMood.creative:
        return Icons.palette; // 🎨 Creative
    }
  }

  String get _moodEmoji {
    switch (widget.mood) {
      case MemoryMood.calm:
        return '🍃'; // Mint tea vibes
      case MemoryMood.emotional:
        return '💖';
      case MemoryMood.focused:
        return '🎯';
      case MemoryMood.love:
        return '💕';
      case MemoryMood.study:
        return '📚';
      case MemoryMood.creative:
        return '✨';
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        transform: Matrix4.identity()..scale(_isHovered ? 1.02 : 1.0),
        child: GestureDetector(
          onTap: () {
            _triggerShimmer();
            widget.onTap?.call();
          },
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [AppColors.pureWhite, Color(0xFFFEFEFE)],
              ),
              boxShadow: [
                BoxShadow(
                  color: _moodColor.withValues(alpha: 0.1),
                  blurRadius: _isHovered ? 15 : 8,
                  spreadRadius: _isHovered ? 3 : 1,
                  offset: const Offset(0, 4),
                ),
                BoxShadow(
                  color: AppColors.lightBlue.withValues(alpha: 0.05),
                  blurRadius: 20,
                  spreadRadius: 0,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Stack(
              children: [
                // Shimmer effect overlay
                AnimatedBuilder(
                  animation: _shimmerAnimation,
                  builder: (context, child) {
                    return ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: ShaderMask(
                        shaderCallback: (bounds) {
                          return LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: [
                              Colors.transparent,
                              AppColors.softBlushPink.withValues(alpha: 0.3),
                              Colors.transparent,
                            ],
                            stops: [
                              _shimmerAnimation.value - 0.3,
                              _shimmerAnimation.value,
                              _shimmerAnimation.value + 0.3,
                            ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
                          ).createShader(bounds);
                        },
                        child: Container(
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.all(Radius.circular(20)),
                          ),
                        ),
                      ),
                    );
                  },
                ),

                // Main card content
                Row(
                  children: [
                    // Mood color bar
                    Container(
                      width: 6,
                      height: 120,
                      decoration: BoxDecoration(
                        color: _moodColor,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(20),
                          bottomLeft: Radius.circular(20),
                        ),
                      ),
                    ),

                    // Card content
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Header with mood and favorite
                            Row(
                              children: [
                                // Mood indicator
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _moodColor.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        _moodEmoji,
                                        style: const TextStyle(fontSize: 12),
                                      ),
                                      const SizedBox(width: 4),
                                      Icon(
                                        _moodIcon,
                                        size: 12,
                                        color: _moodColor,
                                      ),
                                    ],
                                  ),
                                ),

                                const Spacer(),

                                // Favorite button
                                HeartPulseButton(
                                  isActive: widget.memory.isFavorite,
                                  onPressed: widget.onFavoriteToggle,
                                  size: 20,
                                ),

                                // More options
                                PopupMenuButton<String>(
                                  icon: Icon(
                                    Icons.more_vert,
                                    color: AppColors.textLight,
                                    size: 18,
                                  ),
                                  itemBuilder: (context) => [
                                    const PopupMenuItem(
                                      value: 'edit',
                                      child: Row(
                                        children: [
                                          Icon(Icons.edit, size: 16),
                                          SizedBox(width: 8),
                                          Text('Edit'),
                                        ],
                                      ),
                                    ),
                                    const PopupMenuItem(
                                      value: 'delete',
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.delete,
                                            size: 16,
                                            color: AppColors.errorRose,
                                          ),
                                          SizedBox(width: 8),
                                          Text(
                                            'Delete',
                                            style: TextStyle(
                                              color: AppColors.errorRose,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                  onSelected: (value) {
                                    if (value == 'delete') {
                                      widget.onDelete?.call();
                                    }
                                  },
                                ),
                              ],
                            ),

                            const SizedBox(height: 12),

                            // Title
                            Text(
                              widget.memory.title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: AppColors.textPrimary,
                                letterSpacing: 0.2,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),

                            const SizedBox(height: 8),

                            // Content preview
                            Text(
                              AppUtils.truncateText(widget.memory.content, 80),
                              style: const TextStyle(
                                fontSize: 14,
                                color: AppColors.textSecondary,
                                height: 1.4,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),

                            const SizedBox(height: 12),

                            // Footer with tags and timestamp
                            Row(
                              children: [
                                // Tags
                                if (widget.memory.hasTags) ...[
                                  Expanded(
                                    child: Wrap(
                                      spacing: 4,
                                      children: widget.memory.tags.take(2).map((
                                        tag,
                                      ) {
                                        return Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 6,
                                            vertical: 2,
                                          ),
                                          decoration: BoxDecoration(
                                            color: AppColors.lightBlue
                                                .withValues(alpha: 0.1),
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                          ),
                                          child: Text(
                                            tag,
                                            style: const TextStyle(
                                              fontSize: 10,
                                              color: AppColors.textSecondary,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        );
                                      }).toList(),
                                    ),
                                  ),
                                ] else
                                  const Spacer(),

                                // Timestamp
                                Text(
                                  AppUtils.formatRelativeTime(
                                    widget.memory.createdAt,
                                  ),
                                  style: const TextStyle(
                                    fontSize: 11,
                                    color: AppColors.textLight,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Helper function to determine mood from memory content
MemoryMood getMoodFromMemory(MemoryEntry memory) {
  final content = memory.content.toLowerCase();
  final category = memory.category.toLowerCase();

  if (category.contains('love') ||
      content.contains('love') ||
      content.contains('heart')) {
    return MemoryMood.love;
  } else if (category.contains('study') ||
      content.contains('study') ||
      content.contains('learn')) {
    return MemoryMood.study;
  } else if (content.contains('calm') ||
      content.contains('peaceful') ||
      content.contains('relax')) {
    return MemoryMood.calm;
  } else if (content.contains('excited') ||
      content.contains('happy') ||
      content.contains('amazing')) {
    return MemoryMood.emotional;
  } else if (content.contains('focus') ||
      content.contains('work') ||
      content.contains('project')) {
    return MemoryMood.focused;
  } else {
    return MemoryMood.creative;
  }
}
