import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../core/app_theme.dart';

class RomanticVoiceButton extends StatefulWidget {
  final bool isRecording;
  final VoidCallback? onPressed;
  final double size;

  const RomanticVoiceButton({
    super.key,
    required this.isRecording,
    this.onPressed,
    this.size = 80.0,
  });

  @override
  State<RomanticVoiceButton> createState() => _RomanticVoiceButtonState();
}

class _RomanticVoiceButtonState extends State<RomanticVoiceButton>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rippleController;
  late AnimationController _glowController;
  
  late Animation<double> _pulseAnimation;
  late Animation<double> _rippleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    // Pulse animation for the button itself
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Ripple animation for the pink glow
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeOut,
    ));

    // Glow animation for the romantic effect
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _glowAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    if (widget.isRecording) {
      _startRecordingAnimations();
    }
  }

  void _startRecordingAnimations() {
    _pulseController.repeat(reverse: true);
    _rippleController.repeat();
    _glowController.repeat(reverse: true);
  }

  void _stopRecordingAnimations() {
    _pulseController.stop();
    _rippleController.stop();
    _glowController.stop();
    _pulseController.reset();
    _rippleController.reset();
    _glowController.reset();
  }

  @override
  void didUpdateWidget(RomanticVoiceButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isRecording != oldWidget.isRecording) {
      if (widget.isRecording) {
        _startRecordingAnimations();
      } else {
        _stopRecordingAnimations();
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rippleController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onPressed,
      child: SizedBox(
        width: widget.size * 2,
        height: widget.size * 2,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Pink ripple effects
            if (widget.isRecording) ..._buildRipples(),
            
            // Main voice button
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: widget.isRecording ? _pulseAnimation.value : 1.0,
                  child: _buildMainButton(),
                );
              },
            ),
            
            // Romantic glow overlay
            if (widget.isRecording) _buildGlowOverlay(),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildRipples() {
    return List.generate(3, (index) {
      return AnimatedBuilder(
        animation: _rippleController,
        builder: (context, child) {
          final delay = index * 0.3;
          final animationValue = (_rippleController.value - delay).clamp(0.0, 1.0);
          
          return Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.pinkGlow.withOpacity(
                    (1.0 - animationValue) * 0.6,
                  ),
                  width: 2.0,
                ),
              ),
              transform: Matrix4.identity()
                ..scale(0.5 + (animationValue * 1.5)),
            ),
          );
        },
      );
    });
  }

  Widget _buildMainButton() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: widget.isRecording
              ? [
                  AppColors.voiceButtonBlue,
                  AppColors.lightBlue,
                ]
              : [
                  AppColors.voiceButtonBlue,
                  AppColors.lightBlue.withOpacity(0.8),
                ],
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.voiceButtonBlue.withOpacity(0.3),
            blurRadius: widget.isRecording ? 20 : 10,
            spreadRadius: widget.isRecording ? 5 : 2,
            offset: const Offset(0, 4),
          ),
          if (widget.isRecording)
            BoxShadow(
              color: AppColors.pinkGlow.withOpacity(0.2),
              blurRadius: 30,
              spreadRadius: 10,
              offset: const Offset(0, 0),
            ),
        ],
      ),
      child: Icon(
        widget.isRecording ? Icons.stop_rounded : Icons.mic_rounded,
        size: widget.size * 0.4,
        color: AppColors.pureWhite,
      ),
    );
  }

  Widget _buildGlowOverlay() {
    return AnimatedBuilder(
      animation: _glowAnimation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                AppColors.pinkGlow.withOpacity(_glowAnimation.value * 0.3),
                Colors.transparent,
              ],
            ),
          ),
        );
      },
    );
  }
}

class HeartPulseButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final bool isActive;
  final double size;

  const HeartPulseButton({
    super.key,
    this.onPressed,
    this.isActive = false,
    this.size = 24.0,
  });

  @override
  State<HeartPulseButton> createState() => _HeartPulseButtonState();
}

class _HeartPulseButtonState extends State<HeartPulseButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTap() {
    _controller.forward().then((_) {
      _controller.reverse();
    });
    widget.onPressed?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _onTap,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Icon(
              widget.isActive ? Icons.favorite : Icons.favorite_border,
              size: widget.size,
              color: widget.isActive ? AppColors.loveNotes : AppColors.textSecondary,
            ),
          );
        },
      ),
    );
  }
}
